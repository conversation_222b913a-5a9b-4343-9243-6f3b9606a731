package repository

import (
	"context"
	"noorbotv2/internal/models"
)

type DocumentRepository interface {
	SaveDocument(ctx context.Context, doc *models.Document) error
	GetDocuments(ctx context.Context, userID int64, docType models.DocumentType) ([]*models.Document, error)
	GetDocumentByID(ctx context.Context, userID int64, documentID int64) (*models.Document, error)
	DeleteDocument(ctx context.Context, userID int64, documentID int64) error
	UpdateDocumentName(ctx context.Context, userID int64, documentID int64, newName, newPath string) error
	UpdateDocumentLocation(ctx context.Context, userID int64, documentID int64, newType models.DocumentType, newFolder, newPath string) error
	SearchSimilarDocuments(ctx context.Context, userID int64, queryEmbedding []float32, limit int, docType *models.DocumentType) ([]*models.Document, error)
	Health(ctx context.Context) error
}
