package repository

import (
    "context"
    "noorbotv2/internal/models"
)

type DocumentRepository interface {
    SaveDocument(ctx context.Context, doc *models.Document) error
    GetDocuments(ctx context.Context, userID int64, docType models.DocumentType) ([]*models.Document, error)
    SearchSimilarDocuments(ctx context.Context, userID int64, queryEmbedding []float32, limit int, docType *models.DocumentType) ([]*models.Document, error)
    Health(ctx context.Context) error // Assuming Health was also part of it or should be
}
