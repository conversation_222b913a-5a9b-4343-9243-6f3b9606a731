# Build stage
FROM golang:1.22-alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache gcc musl-dev git

# Copy vendored dependencies
COPY vendor/ vendor/
COPY go.mod go.sum ./

# Copy the rest of the source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -mod=vendor -v -ldflags="-w -s" -o main ./cmd/bot

# Final stage
FROM alpine:3.19

WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache ca-certificates tzdata poppler-utils

# Copy the binary from builder
COPY --from=builder /app/main .

# Create necessary directories
RUN mkdir -p /app/storage/knowledge_base /app/storage/private

# Set environment variables
ENV TZ=UTC \
    APP_ENV=production

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8081/health || exit 1

# Run the application
CMD ["./main"]