# Environment variables
.env
.env.*
!.env.example

# Binary files
bin/
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.html

# Dependency directories
vendor/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Log files
*.log

# Database files
*.db
*.sqlite

# Temporary files
tmp/
temp/

# Debug files
debug

# The .qodo directory
.qodo/
# Storage and uploads
storage/
tmp/
*.tmp

# Temporary files
**/tmp/
**/temp/
.qodo
