package file

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"noorbotv2/internal/models"

	"go.uber.org/zap"
)

// MockRepository implements the repository interface for testing
type MockRepository struct {
	documents map[int64]*models.Document
	nextID    int64
}

func NewMockRepository() *MockRepository {
	return &MockRepository{
		documents: make(map[int64]*models.Document),
		nextID:    1,
	}
}

func (m *MockRepository) SaveDocument(ctx context.Context, doc *models.Document) error {
	doc.ID = m.nextID
	m.nextID++
	m.documents[doc.ID] = doc
	return nil
}

func (m *MockRepository) GetDocuments(ctx context.Context, userID int64, docType models.DocumentType) ([]*models.Document, error) {
	var docs []*models.Document
	for _, doc := range m.documents {
		if doc.UserID == userID && doc.Type == docType {
			docs = append(docs, doc)
		}
	}
	return docs, nil
}

func (m *MockRepository) GetDocumentByID(ctx context.Context, userID int64, documentID int64) (*models.Document, error) {
	doc, exists := m.documents[documentID]
	if !exists || doc.UserID != userID {
		return nil, nil
	}
	return doc, nil
}

func (m *MockRepository) DeleteDocument(ctx context.Context, userID int64, documentID int64) error {
	doc, exists := m.documents[documentID]
	if !exists || doc.UserID != userID {
		return nil
	}
	delete(m.documents, documentID)
	return nil
}

func (m *MockRepository) UpdateDocumentName(ctx context.Context, userID int64, documentID int64, newName, newPath string) error {
	doc, exists := m.documents[documentID]
	if !exists || doc.UserID != userID {
		return fmt.Errorf("document not found")
	}
	// Create a copy of the document with updated fields
	updatedDoc := *doc
	updatedDoc.Name = newName
	updatedDoc.Path = newPath
	updatedDoc.UpdatedAt = time.Now()
	m.documents[documentID] = &updatedDoc
	return nil
}

func (m *MockRepository) UpdateDocumentLocation(ctx context.Context, userID int64, documentID int64, newType models.DocumentType, newFolder, newPath string) error {
	doc, exists := m.documents[documentID]
	if !exists || doc.UserID != userID {
		return nil
	}
	doc.Type = newType
	doc.Folder = newFolder
	doc.Path = newPath
	doc.UpdatedAt = time.Now()
	return nil
}

func (m *MockRepository) SearchSimilarDocuments(ctx context.Context, userID int64, queryEmbedding []float32, limit int, docType *models.DocumentType) ([]*models.Document, error) {
	return nil, nil
}

func (m *MockRepository) Health(ctx context.Context) error {
	return nil
}

func TestFileService_SaveFile(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fileservice_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Initialize test dependencies
	logger := zap.NewNop()
	repo := NewMockRepository()
	service := NewFileService(repo, tempDir, logger, nil)

	// Test data
	ctx := context.Background()
	userID := int64(123)
	docType := models.KnowledgeBase
	fileData := []byte("test file content")
	sanitizedFileName := "test.txt"
	originalFileName := "test.txt"
	fileSize := int64(len(fileData))
	mimeType := "text/plain"

	// Test SaveFile
	doc, err := service.SaveFile(ctx, userID, docType, fileData, sanitizedFileName, originalFileName, fileSize, mimeType)
	if err != nil {
		t.Fatalf("SaveFile failed: %v", err)
	}

	// Verify document was created
	if doc.ID == 0 {
		t.Error("Document ID should be set")
	}
	if doc.UserID != userID {
		t.Errorf("Expected UserID %d, got %d", userID, doc.UserID)
	}
	if doc.Type != docType {
		t.Errorf("Expected Type %s, got %s", docType, doc.Type)
	}

	// Verify file was created on filesystem
	expectedPath := filepath.Join(tempDir, string(docType), "123", sanitizedFileName)
	if _, err := os.Stat(expectedPath); os.IsNotExist(err) {
		t.Errorf("File was not created at expected path: %s", expectedPath)
	}

	// Verify file content
	savedContent, err := os.ReadFile(expectedPath)
	if err != nil {
		t.Fatalf("Failed to read saved file: %v", err)
	}
	if string(savedContent) != string(fileData) {
		t.Errorf("File content mismatch. Expected: %s, Got: %s", string(fileData), string(savedContent))
	}
}

func TestFileService_ListFilesByType(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fileservice_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Initialize test dependencies
	logger := zap.NewNop()
	repo := NewMockRepository()
	service := NewFileService(repo, tempDir, logger, nil)

	ctx := context.Background()
	userID := int64(123)

	// Save a test file first
	_, err = service.SaveFile(ctx, userID, models.KnowledgeBase, []byte("test"), "test.txt", "test.txt", 4, "text/plain")
	if err != nil {
		t.Fatalf("Failed to save test file: %v", err)
	}

	// Test ListFilesByType
	docs, err := service.ListFilesByType(ctx, userID, models.KnowledgeBase)
	if err != nil {
		t.Fatalf("ListFilesByType failed: %v", err)
	}

	if len(docs) != 1 {
		t.Errorf("Expected 1 document, got %d", len(docs))
	}

	if len(docs) > 0 && docs[0].Name != "test.txt" {
		t.Errorf("Expected document name 'test.txt', got '%s'", docs[0].Name)
	}
}

func TestFileService_DeleteFile(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fileservice_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Initialize test dependencies
	logger := zap.NewNop()
	repo := NewMockRepository()
	service := NewFileService(repo, tempDir, logger, nil)

	ctx := context.Background()
	userID := int64(123)

	// Save a test file first
	doc, err := service.SaveFile(ctx, userID, models.KnowledgeBase, []byte("test"), "test.txt", "test.txt", 4, "text/plain")
	if err != nil {
		t.Fatalf("Failed to save test file: %v", err)
	}

	// Test DeleteFile
	err = service.DeleteFile(ctx, userID, doc.ID)
	if err != nil {
		t.Fatalf("DeleteFile failed: %v", err)
	}

	// Verify file was deleted from database
	docs, err := service.ListFilesByType(ctx, userID, models.KnowledgeBase)
	if err != nil {
		t.Fatalf("Failed to list files: %v", err)
	}
	if len(docs) != 0 {
		t.Errorf("Expected 0 documents after deletion, got %d", len(docs))
	}

	// Verify file was deleted from filesystem
	if _, err := os.Stat(doc.Path); !os.IsNotExist(err) {
		t.Error("File should have been deleted from filesystem")
	}
}

func TestFileService_RenameFile(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fileservice_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Initialize test dependencies
	logger := zap.NewNop()
	repo := NewMockRepository()
	service := NewFileService(repo, tempDir, logger, nil)

	ctx := context.Background()
	userID := int64(123)

	// Save a test file first
	doc, err := service.SaveFile(ctx, userID, models.KnowledgeBase, []byte("test"), "test.txt", "test.txt", 4, "text/plain")
	if err != nil {
		t.Fatalf("Failed to save test file: %v", err)
	}

	// Test RenameFile
	newName := "renamed_test"
	err = service.RenameFile(ctx, userID, doc.ID, newName)
	if err != nil {
		t.Fatalf("RenameFile failed: %v", err)
	}

	// Verify file was renamed in database
	updatedDoc, err := repo.GetDocumentByID(ctx, userID, doc.ID)
	if err != nil {
		t.Fatalf("Failed to get updated document: %v", err)
	}
	expectedName := newName + ".txt" // Extension should be preserved
	if updatedDoc.Name != expectedName {
		t.Errorf("Expected document name '%s', got '%s'", expectedName, updatedDoc.Name)
	}

	// Verify file was renamed in filesystem
	if _, err := os.Stat(updatedDoc.Path); os.IsNotExist(err) {
		t.Error("Renamed file should exist in filesystem")
	}
	// Note: doc.Path and updatedDoc.Path should be different after rename
	if doc.Path == updatedDoc.Path {
		t.Error("File path should have changed after rename")
	}
}

func TestFileService_DownloadFile(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "fileservice_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Initialize test dependencies
	logger := zap.NewNop()
	repo := NewMockRepository()
	service := NewFileService(repo, tempDir, logger, nil)

	ctx := context.Background()
	userID := int64(123)
	testContent := []byte("test file content for download")

	// Save a test file first
	doc, err := service.SaveFile(ctx, userID, models.Private, testContent, "download_test.txt", "download_test.txt", int64(len(testContent)), "text/plain")
	if err != nil {
		t.Fatalf("Failed to save test file: %v", err)
	}

	// Test DownloadFile
	fileData, fileName, err := service.DownloadFile(ctx, userID, doc.ID)
	if err != nil {
		t.Fatalf("DownloadFile failed: %v", err)
	}

	// Verify downloaded content
	if string(fileData) != string(testContent) {
		t.Errorf("Downloaded content mismatch. Expected: %s, Got: %s", string(testContent), string(fileData))
	}
	if fileName != "download_test.txt" {
		t.Errorf("Expected filename 'download_test.txt', got '%s'", fileName)
	}
}
