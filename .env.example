# Database
DB_HOST=localhost
DB_PORT=5434
DB_USER=noorbot
DB_PASSWORD=noorbot999
DB_NAME=noorbot
DB_MAX_CONNS=25
DB_MAX_IDLE_TIME=5m
DB_HEALTH_CHECK=30s

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Telegram
TELEGRAM_TOKEN=your_bot_token_here
ALLOWED_USERS=user1,user2

# AI Services
DEEPSEEK_API_KEY=your_deepseek_key_here # Corresponds to DEEPSEEK_TOKEN in config.go
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_TEMPERATURE=0.7
DEEPSEEK_MAX_TOKENS=1024
GROK_API_KEY=your_grok_key_here

# File Storage
UPLOAD_MAX_SIZE=150000000
STORAGE_PATH=./storage
TEMP_PATH=./tmp

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=2112

# Development
ENV=development
DEBUG=true
