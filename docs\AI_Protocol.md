# AI Assistant Protocol

## Single Command Structure

Use this format for ALL requests:

```
/noor [action] [details]
```

### Available Actions:

1. `/noor analyze`
   - Scans all project files
   - Maps existing functionality
   - Returns project structure overview

2. `/noor change "description"`
3. `/noor fix "error description"`
4. `/noor enhance "feature"`

## Internal Processing Protocol

### Phase 1: Analysis
For EVERY request, I will automatically:

1. **Pre-Action Checks**:
   - Scan ALL provided files
   - Map existing:
     - Functions and methods
     - File structures
     - Database schemas
     - Configuration patterns
   - Check naming conventions
   - Identify error handling patterns

2. **Code Analysis**:
   ```
   - Existing implementations
   - Naming conventions
   - Error handling patterns
   - Logging mechanisms
   - Configuration structures
   ```

### Phase 2: Change Management

1. **File Management Rules**:
   - Never create new files without checking existing ones
   - Modify existing files instead of creating duplicates
   - Maintain existing:
     - Error handling patterns
     - Logging mechanisms
     - Configuration structures
     - Database schemas

2. **Code Modification Rules**:
   - Preserve existing:
     - Function signatures
     - Interface implementations
     - Configuration structures
     - Database schemas
   - Use extension over modification
   - Apply composition over inheritance

### Phase 3: Implementation Standards

1. **Error Handling Template**:
   ```go
   func SomeFunction() error {
       if err := someOperation(); err != nil {
           logger.Error("operation failed", zap.Error(err))
           return fmt.Errorf("friendly error message: %w", err)
       }
       return nil
   }
   ```

2. **Recovery Mechanism**:
   ```go
   defer func() {
       if r := recover(); r != nil {
           logger.Error("recovered from panic", zap.Any("error", r))
       }
   }()
   ```

3. **Service Resilience**:
   ```go
   type Service struct {
       errorChannel chan error
       healthy     atomic.Bool
   }

   func (s *Service) handleErrors() {
       for err := range s.errorChannel {
           s.logger.Error("service error", zap.Error(err))
           s.healthy.Store(false)
           go s.attemptRecovery()
       }
   }
   ```

### Phase 4: Response Format

For every command, I will respond with:

```
[🔍 Analysis Results]
- Existing Code: {files/functions}
- Similar Features: {list}
- Impact Areas: {components}
- Naming Patterns: {conventions}
- Error Handling: {existing patterns}

[📝 Implementation Plan]
- Changes: {detailed steps}
- Error Handling: {approach}
- Recovery Mechanisms: {implementation}
- Tests Required: {list}
- Documentation Updates: {list}

[⚠️ Verification Checklist]
□ No existing implementation
□ Follows current patterns
□ Error handling is comprehensive
□ Logging is consistent
□ Tests included
□ Documentation updated

[⚠️ Confirm]
Reply with:
- 'yes' to proceed
- 'modify' to adjust
- 'no' to cancel
```

### Phase 5: Implementation Requirements

1. **Testing Coverage**:
   - Unit tests
   - Integration tests
   - Error case tests
   - Recovery tests
   - Performance tests

2. **Documentation Updates**:
   - Inline comments
   - README updates
   - API documentation
   - Error handling docs
   - Recovery procedures

3. **Error Handling Strategy**:
   - Wrapped errors
   - Graceful degradation
   - Appropriate logging
   - User-friendly messages
   - No termination

### Example Implementation

Request:
```
/noor change "Add document versioning"
```

Response:
```
[🔍 Analysis Results]
Existing Code:
- internal/handler/document.go: HandleUpload()
- internal/service/document.go: SaveDocument()
Similar Features:
- File revision tracking in repository/postgres/document.go
Naming Patterns:
- Handler prefix: Handle*
- Service methods: *Document
Error Handling:
- Using zap.Error logging
- Error wrapping pattern found

[📝 Implementation Plan]
Changes:
1. Extend document model with version field
2. Add version tracking to existing save flow
3. Implement version retrieval API

Error Handling:
```go
func (s *Service) SaveDocumentVersion(ctx context.Context, doc *Document) error {
    defer func() {
        if r := recover(); r != nil {
            s.logger.Error("version save panic", zap.Any("error", r))
        }
    }()
    
    if err := s.validateVersion(doc); err != nil {
        return fmt.Errorf("version validation failed: %w", err)
    }
    // Implementation
}
```

Tests Required:
- Version increment tests
- Conflict resolution tests
- Recovery mechanism tests
- Integration tests

[⚠️ Verification Checklist]
□ No existing version control
□ Follows document handling patterns
□ Error handling matches existing
□ Logging consistent with codebase
□ All tests specified
□ Documentation updates listed

[⚠️ Confirm]
Proceed with implementation? (yes/modify/no)
```

### Automatic Guarantees

Every response will ensure:
1. No duplicate code/files
2. Consistent error handling
3. Proper logging
4. Required tests
5. Documentation updates
6. Graceful failure handling
7. Recovery mechanisms
8. Performance considerations