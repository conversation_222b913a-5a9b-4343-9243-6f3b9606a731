package ai

import (
	"context"
	"errors"
	"testing"
	"time"

	"noorbotv2/internal/config"
	"noorbotv2/internal/models"
	"noorbotv2/internal/service/cache"

	"go.uber.org/zap"
)

// MockRepository implements the repository interface for testing
type MockRepository struct {
	documents map[int64]*models.Document
	nextID    int64
}

func NewMockRepository() *MockRepository {
	return &MockRepository{
		documents: make(map[int64]*models.Document),
		nextID:    1,
	}
}

func (m *MockRepository) SaveDocument(ctx context.Context, doc *models.Document) error {
	doc.ID = m.nextID
	m.nextID++
	m.documents[doc.ID] = doc
	return nil
}

func (m *MockRepository) GetDocuments(ctx context.Context, userID int64, docType models.DocumentType) ([]*models.Document, error) {
	var docs []*models.Document
	for _, doc := range m.documents {
		if doc.UserID == userID && doc.Type == docType {
			docs = append(docs, doc)
		}
	}
	return docs, nil
}

func (m *MockRepository) GetDocumentByID(ctx context.Context, userID int64, documentID int64) (*models.Document, error) {
	doc, exists := m.documents[documentID]
	if !exists || doc.UserID != userID {
		return nil, nil
	}
	return doc, nil
}

func (m *MockRepository) DeleteDocument(ctx context.Context, userID int64, documentID int64) error {
	delete(m.documents, documentID)
	return nil
}

func (m *MockRepository) UpdateDocumentName(ctx context.Context, userID int64, documentID int64, newName, newPath string) error {
	return nil
}

func (m *MockRepository) UpdateDocumentLocation(ctx context.Context, userID int64, documentID int64, newType models.DocumentType, newFolder, newPath string) error {
	return nil
}

func (m *MockRepository) SearchSimilarDocuments(ctx context.Context, userID int64, queryEmbedding []float32, limit int, docType *models.DocumentType) ([]*models.Document, error) {
	// Return some mock documents for testing
	var docs []*models.Document
	for _, doc := range m.documents {
		if doc.UserID == userID {
			docs = append(docs, doc)
			if len(docs) >= limit {
				break
			}
		}
	}
	return docs, nil
}

func (m *MockRepository) Health(ctx context.Context) error {
	return nil
}

// MockCache implements a simple in-memory cache for testing
type MockCache struct {
	data map[string]interface{}
}

func NewMockCache() *MockCache {
	return &MockCache{
		data: make(map[string]interface{}),
	}
}

func (c *MockCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	c.data[key] = value
	return nil
}

func (c *MockCache) Get(ctx context.Context, key string) (interface{}, error) {
	value, exists := c.data[key]
	if !exists {
		return nil, errors.New("cache key not found")
	}
	return value, nil
}

func (c *MockCache) Delete(ctx context.Context, key string) error {
	delete(c.data, key)
	return nil
}

func (c *MockCache) Close() error {
	return nil
}

func TestAIService_QueryDocuments(t *testing.T) {
	// Initialize test dependencies
	logger := zap.NewNop()
	repo := NewMockRepository()

	// Create AI config
	aiCfg := config.AIConfig{
		DeepSeekToken:       "test-token",
		DeepSeekModel:       "deepseek-chat",
		DeepSeekTemperature: 0.7,
		DeepSeekMaxTokens:   1000,
	}

	// Create AI service
	aiService, err := NewAIService(repo, &cache.RedisCache{}, logger, aiCfg)
	if err != nil {
		t.Fatalf("Failed to create AI service: %v", err)
	}

	ctx := context.Background()
	userID := int64(123)

	// Add some test documents to the repository
	doc1 := &models.Document{
		UserID:   userID,
		Name:     "test1.txt",
		Type:     models.KnowledgeBase,
		Path:     "/tmp/test1.txt",
		MIMEType: "text/plain",
		Size:     100,
	}
	repo.SaveDocument(ctx, doc1)

	doc2 := &models.Document{
		UserID:   userID,
		Name:     "test2.pdf",
		Type:     models.KnowledgeBase,
		Path:     "/tmp/test2.pdf",
		MIMEType: "application/pdf",
		Size:     200,
	}
	repo.SaveDocument(ctx, doc2)

	// Test QueryDocuments
	query := "What is the content of my documents?"

	// Note: This test will fail with the actual DeepSeek API call since we don't have a real token
	// But it will test our logic up to that point
	response, err := aiService.QueryDocuments(ctx, userID, query)

	// We expect this to work with our mock data, even though the DeepSeek call might fail
	if err != nil {
		t.Logf("Expected error due to mock DeepSeek token: %v", err)
		// This is expected in test environment
	}

	if response == "" {
		t.Error("Response should not be empty")
	}

	t.Logf("AI Service response: %s", response)
}

func TestAIService_GenerateQueryEmbedding(t *testing.T) {
	// Initialize test dependencies
	logger := zap.NewNop()
	repo := NewMockRepository()

	aiCfg := config.AIConfig{
		DeepSeekToken:       "test-token",
		DeepSeekModel:       "deepseek-chat",
		DeepSeekTemperature: 0.7,
		DeepSeekMaxTokens:   1000,
	}

	aiService, err := NewAIService(repo, &cache.RedisCache{}, logger, aiCfg)
	if err != nil {
		t.Fatalf("Failed to create AI service: %v", err)
	}

	ctx := context.Background()
	query := "test query"

	// Test embedding generation
	embedding, err := aiService.generateQueryEmbedding(ctx, query)
	if err != nil {
		t.Fatalf("Failed to generate embedding: %v", err)
	}

	// Check that we got a valid embedding
	if len(embedding) != 1536 {
		t.Errorf("Expected embedding length 1536, got %d", len(embedding))
	}

	// Check that all values are 0.1 (our mock value)
	for i, val := range embedding {
		if val != 0.1 {
			t.Errorf("Expected embedding[%d] = 0.1, got %f", i, val)
			break
		}
	}
}

func TestAIService_BuildDocumentContext(t *testing.T) {
	// Initialize test dependencies
	logger := zap.NewNop()
	repo := NewMockRepository()

	aiCfg := config.AIConfig{
		DeepSeekToken:       "test-token",
		DeepSeekModel:       "deepseek-chat",
		DeepSeekTemperature: 0.7,
		DeepSeekMaxTokens:   1000,
	}

	aiService, err := NewAIService(repo, &cache.RedisCache{}, logger, aiCfg)
	if err != nil {
		t.Fatalf("Failed to create AI service: %v", err)
	}

	ctx := context.Background()

	// Create test documents
	docs := []*models.Document{
		{
			Name:     "test1.txt",
			Type:     models.KnowledgeBase,
			Path:     "/tmp/test1.txt",
			MIMEType: "text/plain",
		},
		{
			Name:     "test2.pdf",
			Type:     models.Private,
			Path:     "/tmp/test2.pdf",
			MIMEType: "application/pdf",
		},
	}

	// Test building document context
	context, err := aiService.buildDocumentContext(ctx, docs)
	if err != nil {
		t.Fatalf("Failed to build document context: %v", err)
	}

	// Check that context contains document information
	if context == "" {
		t.Error("Document context should not be empty")
	}

	// Check that context contains document names
	if !contains(context, "test1.txt") {
		t.Error("Context should contain test1.txt")
	}
	if !contains(context, "test2.pdf") {
		t.Error("Context should contain test2.pdf")
	}

	t.Logf("Document context: %s", context)
}

// Helper function to check if string contains substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || containsAt(s, substr)))
}

func containsAt(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
