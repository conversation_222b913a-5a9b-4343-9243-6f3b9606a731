#!/bin/bash
set -e

echo "Starting project restructuring..."

# Create new directory structure
mkdir -p internal/{config,handler/telegram,models,repository/migrations,service/{ai,file,cache},health,monitoring}

# Create initial service files if they don't exist
touch internal/service/ai/deepseek_client.go
touch internal/service/ai/ai_service.go
touch internal/service/ai/embedding_service.go
touch internal/service/ai/vector_search.go
touch internal/service/file/file_service.go
touch internal/service/cache/redis_cache.go
touch internal/service/cache/rate_limiter.go
touch internal/service/ai/bot_service.go

# Create handler file
touch internal/handler/telegram/handler.go

echo "Restructuring complete!"
