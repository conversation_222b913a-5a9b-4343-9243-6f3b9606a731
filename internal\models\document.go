package models

import (
    "time"
)

type DocumentType string

const (
    KnowledgeBase DocumentType = "knowledge_base"
    Private       DocumentType = "private"
)

type Document struct {
    ID        int64        `json:"id"`
    UserID    int64        `json:"user_id"`
    Name      string       `json:"name"`
    Type      DocumentType `json:"type"`
    Path      string       `json:"path"`
    Folder    string       `json:"folder"`
    MIMEType  string       `json:"mime_type,omitempty"`
    Content   string       `json:"content"`
    Size      int64       `json:"size"`
    Embedding []float32    `json:"embedding,omitempty"`
    CreatedAt time.Time    `json:"created_at"`
    UpdatedAt time.Time    `json:"updated_at"`
}
