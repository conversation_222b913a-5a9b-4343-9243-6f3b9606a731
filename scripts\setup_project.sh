#!/bin/bash
set -e

echo "Setting up project..."

# Create main directories if they don't exist
mkdir -p internal/{config,handler/telegram,models,repository,service/{ai,file,cache}}

# Create initial files if they don't exist
touch internal/handler/telegram/handler.go
touch internal/service/ai/deepseek_client.go
touch internal/service/ai/ai_service.go
touch internal/service/ai/embedding_service.go
touch internal/service/ai/vector_search.go
touch internal/service/file/file_service.go
touch internal/service/cache/redis_cache.go
touch internal/service/cache/rate_limiter.go

# Consolidate migrations
mkdir -p internal/repository/migrations
if [ -d "backup_current/migrations" ]; then
    cp backup_current/migrations/* internal/repository/migrations/ 2>/dev/null || true
fi

echo "Setup complete!"
