package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"noorbotv2/internal/config"
	"noorbotv2/internal/handler/telegram"
	"noorbotv2/internal/repository"
	"noorbotv2/internal/service/ai"
	"noorbotv2/internal/service/cache"
	"noorbotv2/internal/service/file"

	"go.uber.org/zap"
	tele "gopkg.in/telebot.v3"
)

func main() {
	ctx := context.Background()

	// Initialize logger first
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.Fatal("Failed to load config", zap.Error(err))
	}
	logger.Info("Configuration loaded successfully",
		zap.String("db_host", cfg.DB.Host),
		zap.String("redis_host", cfg.Redis.Host))

	// Initialize repository
	repo, err := repository.NewPostgresRepository(cfg.GetDBConnString())
	if err != nil {
		logger.Fatal("Failed to initialize repository", zap.Error(err))
	}
	defer repo.Close(ctx)
	logger.Info("Repository initialized successfully")

	// Initialize cache
	cacheService, err := cache.NewRedisCache(ctx, cfg.Redis.Host, cfg.Redis.Password, cfg.Redis.DB)
	if err != nil {
		logger.Fatal("Failed to initialize cache service", zap.Error(err))
	}
	logger.Info("Cache service initialized")

	// Initialize services
	// TODO: Initialize TextExtractService first as FileService depends on it
	// textractService := textract.NewTextExtractService(logger)
	// logger.Info("TextExtract service initialized")

	fileService := file.NewFileService(repo, cfg.Storage.Path, logger, nil) // Pass nil for now
	logger.Info("File service initialized")

	aiService, err := ai.NewAIService(repo, cacheService, logger, cfg.AI) // Pass cfg.AI
	if err != nil {
		logger.Fatal("Failed to initialize AI service", zap.Error(err))
	}
	logger.Info("AI service initialized")

	// Initialize bot with detailed logging
	logger.Info("Initializing Telegram bot", zap.String("token_prefix", cfg.Telegram.Token[:5]+"..."))
	bot, err := tele.NewBot(tele.Settings{
		Token:  cfg.Telegram.Token,
		Poller: &tele.LongPoller{Timeout: 10 * time.Second},
	})
	if err != nil {
		logger.Fatal("Failed to create bot", zap.Error(err))
	}
	logger.Info("Telegram bot initialized successfully")

	// Initialize rate limiter
	rateLimiter := cache.NewRateLimiter(cacheService, 15, time.Minute)
	logger.Info("Rate limiter initialized")

	// Initialize handler
	handler := telegram.NewHandler(
		bot,
		fileService,
		aiService,
		logger,
		cfg.Telegram.AllowedUsers,
		rateLimiter,
		cacheService,
	)
	handler.RegisterHandlers()
	logger.Info("Handlers registered successfully")

	// Start bot
	logger.Info("Starting bot polling...")
	go bot.Start() // Remove the error check since it's handled internally

	logger.Info("Bot polling started successfully")

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	// Graceful shutdown
	logger.Info("Shutting down...")
	bot.Stop()
}
