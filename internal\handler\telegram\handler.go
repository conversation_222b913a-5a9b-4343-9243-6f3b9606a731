package telegram

import (
	"fmt"
	"fmt"
	"io"
	"path/filepath"
	"strconv"
	"strings"
	"time" // Added for date formatting

	"go.uber.org/zap"
	tele "gopkg.in/telebot.v3"
	"noorbotv2/internal/models"
	"noorbotv2/internal/service/ai"
	"noorbotv2/internal/service/cache"
	"noorbotv2/internal/service/file"
)

const (
	MaxFileSize        = 10 * 1024 * 1024 // 10 MB
	MaxFilenameLength  = 200
	pendingFileKeyPrefix = "pending_upload:"
	pendingFileTTL     = 15 * time.Minute
)

var AllowedMimeTypes = map[string]bool{
    "application/pdf": true,
    "text/plain":      true,
    "image/jpeg":      true,
    "image/png":       true,
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": true, // DOCX
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": true, // PPTX
}

type Handler struct {
    bot          *tele.Bot
    fileService  *file.Service
    aiService    *ai.Service
    rateLimiter  *cache.RateLimiter
    cache        *cache.RedisCache // Add this line
    logger       *zap.Logger
    allowedUsers []int64
    // Define button menu
    menu struct {
        home              *tele.ReplyMarkup
        files             *tele.ReplyMarkup
        // actions field removed
        fileTypeSelection *tele.ReplyMarkup // New field for inline keyboard
    }
}

func (h *Handler) isUserAllowed(userID int64) bool {
    if len(h.allowedUsers) == 0 {
        // Deny access if the allowedUsers list is empty. 
        // For the bot to be open, the ALLOWED_USERS env var must be explicitly set (even if to a dummy value like "0" if truly open access was desired after careful consideration, though not recommended).
        // Or, a separate explicit "public_mode" config could be introduced if needed.
        return false 
    }
    for _, allowedID := range h.allowedUsers {
        if allowedID == userID {
            return true
        }
    }
    return false
}

func (h *Handler) authMiddleware(next tele.HandlerFunc) tele.HandlerFunc {
    return func(c tele.Context) error {
        if !h.isUserAllowed(c.Sender().ID) {
            h.logger.Warn("Unauthorized access attempt", 
                zap.Int64("user_id", c.Sender().ID))
            return c.Reply("Sorry, you are not authorized to use this bot.")
        }
        return next(c)
    }
}

func NewHandler(
    bot *tele.Bot,
    fileService *file.Service,
    aiService *ai.Service,
    logger *zap.Logger,
    allowedUsers []int64,
    rateLimiter *cache.RateLimiter,
    theCache *cache.RedisCache, // Add this parameter
) *Handler {
    h := &Handler{
        bot:          bot,
        fileService:  fileService,
        aiService:    aiService,
        logger:       logger,
        allowedUsers: allowedUsers,
        rateLimiter:  rateLimiter,
        cache:        theCache, // Add this assignment
    }

    // Initialize menus
    h.initializeMenus()
    return h
}

func (h *Handler) initializeMenus() {
    // Initialize main menu
    h.menu.home = &tele.ReplyMarkup{ResizeKeyboard: true}
    h.menu.home.Reply(
        h.menu.home.Row(
            h.menu.home.Text("🏠 Home"),
            h.menu.home.Text("📁 My Files"),
        ),
        h.menu.home.Row(
            h.menu.home.Text("❓ Help"),
            h.menu.home.Text("🔍 Search"),
            h.menu.home.Text("[Ask]"),
        ),
    )

    // Initialize files menu
    h.menu.files = &tele.ReplyMarkup{ResizeKeyboard: true}
    h.menu.files.Reply(
        h.menu.files.Row(
            h.menu.files.Text("📚 Knowledge Base"),
            h.menu.files.Text("🔒 Private"),
        ),
        h.menu.files.Row(
            h.menu.files.Text("↩️ Back to Home"),
            h.menu.files.Text("[Ask]"),
        ),
    )

    // Inline keyboard for file type selection
    selectorFileType := &tele.ReplyMarkup{}
    btnKBDocs := selectorFileType.Data("📚 Knowledge Base", "select_type_kb", "kb")
    btnPrivateDocs := selectorFileType.Data("🔒 Private", "select_type_private", "private")
    selectorFileType.Inline(selectorFileType.Row(btnKBDocs, btnPrivateDocs))
    h.menu.fileTypeSelection = selectorFileType
}

func (h *Handler) RegisterHandlers() {
    h.logger.Info("Registering handlers...")

    // Register /start command with auth middleware and menu
    h.bot.Handle("/start", h.authMiddleware(func(c tele.Context) error {
        userID := c.Sender().ID
        allowed, err := h.rateLimiter.Allow(c.Request().Context(), strconv.FormatInt(userID, 10))
        if err != nil {
            h.logger.Error("Rate limiter check failed", zap.Error(err), zap.Int64("user_id", userID))
            return c.Reply("Sorry, something went wrong. Please try again in a moment.")
        }
        if !allowed {
            h.logger.Info("Rate limit exceeded for /start", zap.Int64("user_id", userID))
            return c.Reply("You are sending too many requests. Please wait a moment and try again.")
        }

        h.logger.Info("Received /start command", zap.Int64("user_id", userID))
        return c.Reply(
            "Hello! I'm NoorBot. How can I help you today?",
            h.menu.home,
        )
    }))

    // Register main menu buttons
    h.bot.Handle("🏠 Home", h.authMiddleware(h.handleHome))
    h.bot.Handle("📁 My Files", h.authMiddleware(h.handleFiles))
    h.bot.Handle("❓ Help", h.authMiddleware(h.handleHelp))
    h.bot.Handle("🔍 Search", h.authMiddleware(h.handleSearch))
    h.bot.Handle("[Ask]", h.authMiddleware(h.handleAsk))

    // Register files menu buttons
    h.bot.Handle("📚 Knowledge Base", h.authMiddleware(h.handleKnowledgeBase))
    h.bot.Handle("🔒 Private", h.authMiddleware(h.handlePrivate))
    h.bot.Handle("↩️ Back to Home", h.authMiddleware(h.handleHome))

    // Register file handlers
    h.bot.Handle(tele.OnDocument, h.authMiddleware(h.handleDocument))
    h.bot.Handle(tele.OnText, h.authMiddleware(h.handleText))

    // Register inline keyboard callback handlers for file type selection
    // These unique strings ("select_type_kb", "select_type_private") must match
    // the 'unique' part of the Data buttons defined in initializeMenus.
    // Telebot v3 uses "\f{unique}" for these global callback handlers.
    h.bot.Handle("\fselect_type_kb", h.authMiddleware(h.handleSelectTypeKnowledgeBase))
    h.bot.Handle("\fselect_type_private", h.authMiddleware(h.handleSelectTypePrivate))

    h.logger.Info("Handlers registered successfully")
}

// Handler implementations
func (h *Handler) handleHome(c tele.Context) error {
    return c.Reply("Welcome to the main menu!", h.menu.home)
}

func (h *Handler) handleFiles(c tele.Context) error {
    return c.Reply("Please select a file category:", h.menu.files)
}

func (h *Handler) handleHelp(c tele.Context) error {
    helpText := `
*Available Commands:*
• Use 📁 My Files to manage your documents
• Use 🔍 Search to find specific information
• Upload documents directly to store them
• Ask questions about your stored knowledge

*Need more help?*
Feel free to ask specific questions!
`
    return c.Reply(helpText, &tele.SendOptions{ParseMode: tele.ModeMarkdown})
}

func (h *Handler) handleSearch(c tele.Context) error {
    return c.Reply("Please enter your search query or question:")
}

func (h *Handler) handleAsk(c tele.Context) error {
    userID := c.Sender().ID
    allowed, err := h.rateLimiter.Allow(c.Request().Context(), strconv.FormatInt(userID, 10))
    if err != nil {
        h.logger.Error("Rate limiter check failed for handleAsk", zap.Error(err), zap.Int64("user_id", userID))
        return c.Reply("Sorry, something went wrong. Please try again in a moment.")
    }
    if !allowed {
        h.logger.Info("Rate limit exceeded for handleAsk", zap.Int64("user_id", userID))
        return c.Reply("You are sending too many requests. Please wait a moment and try again.")
    }
    return c.Reply("What would you like to ask? Please type your question.")
}

func (h *Handler) handleKnowledgeBase(c tele.Context) error {
    userID := c.Sender().ID
    h.logger.Info("Handling Knowledge Base file listing", zap.Int64("user_id", userID))

    docs, err := h.fileService.ListFilesByType(c.Request().Context(), userID, models.KnowledgeBase)
    if err != nil {
        h.logger.Error("Failed to list Knowledge Base files", zap.Int64("user_id", userID), zap.Error(err))
        return c.Reply("Could not retrieve your Knowledge Base files at the moment. Please try again later.", h.menu.files)
    }

    if len(docs) == 0 {
        return c.Reply("You don't have any files in your Knowledge Base yet. Upload some documents to get started!", h.menu.files)
    }

    var sb strings.Builder
    sb.WriteString("*Your Knowledge Base Files:*\n\n")
    for i, doc := range docs {
        // Format: "1. FileName.pdf (Uploaded: YYYY-MM-DD HH:MM)"
        sb.WriteString(fmt.Sprintf("%d. *%s*\n   Uploaded: %s\n",
            i+1,
            doc.Name,
            doc.CreatedAt.Format("2006-01-02 15:04"),
        ))
    }
    return c.Reply(sb.String(), &tele.SendOptions{ParseMode: tele.ModeMarkdown}, h.menu.files)
}

func (h *Handler) handlePrivate(c tele.Context) error {
    userID := c.Sender().ID
    h.logger.Info("Handling Private file listing", zap.Int64("user_id", userID))

    docs, err := h.fileService.ListFilesByType(c.Request().Context(), userID, models.Private)
    if err != nil {
        h.logger.Error("Failed to list Private files", zap.Int64("user_id", userID), zap.Error(err))
        return c.Reply("Could not retrieve your Private files at the moment. Please try again later.", h.menu.files)
    }

    if len(docs) == 0 {
        return c.Reply("You don't have any Private files yet. Upload some documents to get started!", h.menu.files)
    }

    var sb strings.Builder
    sb.WriteString("*Your Private Files:*\n\n")
    for i, doc := range docs {
        sb.WriteString(fmt.Sprintf("%d. *%s*\n   Uploaded: %s\n",
            i+1,
            doc.Name,
            doc.CreatedAt.Format("2006-01-02 15:04"),
        ))
    }
    return c.Reply(sb.String(), &tele.SendOptions{ParseMode: tele.ModeMarkdown}, h.menu.files)
}

func (h *Handler) handleDocument(c tele.Context) error {
    userID := c.Sender().ID
    allowed, err := h.rateLimiter.Allow(c.Request().Context(), strconv.FormatInt(userID, 10))
    if err != nil {
        h.logger.Error("Rate limiter check failed for handleDocument", zap.Error(err), zap.Int64("user_id", userID))
        return c.Reply("Sorry, something went wrong. Please try again in a moment.")
    }
    if !allowed {
        h.logger.Info("Rate limit exceeded for handleDocument", zap.Int64("user_id", userID))
        return c.Reply("You are sending too many requests. Please wait a moment and try again.")
    }

    doc := c.Message().Document
    h.logger.Info("Received document",
        zap.String("file_name", doc.FileName),
        zap.Int64("file_size", doc.FileSize),
        zap.String("mime_type", doc.MIMEType),
        zap.Int64("user_id", userID))

    // File Size Check
    if doc.FileSize > MaxFileSize {
        h.logger.Warn("File rejected: too large",
            zap.String("file_name", doc.FileName),
            zap.Int64("file_size", doc.FileSize),
            zap.Int64("user_id", userID))
        return c.Reply("File is too large. Maximum size is 10MB.")
    }

    // MIME Type Check
    if !AllowedMimeTypes[doc.MIMEType] {
        h.logger.Warn("File rejected: invalid MIME type",
            zap.String("file_name", doc.FileName),
            zap.String("mime_type", doc.MIMEType),
            zap.Int64("user_id", userID))
        return c.Reply("File type not allowed. Allowed types are: PDF, TXT, JPG, PNG, DOCX, PPTX.")
    }

    // Filename Sanitization
    sanitizedFileName := filepath.Base(doc.FileName) // Remove directory paths
    // Replace potentially problematic characters (allow alphanumeric, '.', '_', '-')
    var sb strings.Builder
    for _, r := range sanitizedFileName {
        if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '.' || r == '_' || r == '-' {
            sb.WriteRune(r)
        } else {
            sb.WriteRune('_') // Replace with underscore
        }
    }
    sanitizedFileName = sb.String()

    // Ensure filename is not excessively long
    if len(sanitizedFileName) > MaxFilenameLength {
        ext := filepath.Ext(sanitizedFileName)
        baseName := strings.TrimSuffix(sanitizedFileName, ext)
        if len(baseName) > MaxFilenameLength-len(ext) {
            baseName = baseName[:MaxFilenameLength-len(ext)]
        }
        sanitizedFileName = baseName + ext
    }
    
    h.logger.Info("Document validated successfully",
        zap.String("original_name", doc.FileName),
        zap.String("sanitized_name", sanitizedFileName),
        zap.Int64("user_id", userID))

    // Get File Content
    fileReader, err := h.bot.File(&doc.File)
    if err != nil {
        h.logger.Error("Failed to get file from Telegram",
            zap.String("file_id", doc.File.FileID),
            zap.Error(err),
            zap.Int64("user_id", userID))
        return c.Reply("Could not download file, please try again.")
    }
    defer fileReader.Close()

    fileData, err := io.ReadAll(fileReader)
    if err != nil {
        h.logger.Error("Failed to read file data",
            zap.String("file_id", doc.File.FileID),
            zap.Error(err),
            zap.Int64("user_id", userID))
        return c.Reply("Could not read file data, please try again.")
    }

    // Store pending file info in Redis
    pendingInfo := PendingFileInfo{
        SanitizedFileName: sanitizedFileName,
        OriginalFileName:  doc.FileName,
        FileData:          fileData,
        FileSize:          doc.FileSize,
        MIMEType:          doc.MIMEType,
        TelebotFileID:     doc.File.FileID,
    }
    redisKey := pendingFileKeyPrefix + strconv.FormatInt(userID, 10)

    cacheErr := h.cache.Set(c.Request().Context(), redisKey, pendingInfo, pendingFileTTL)
    if cacheErr != nil {
        h.logger.Error("Failed to store pending file info in Redis",
            zap.Error(cacheErr),
            zap.Int64("user_id", userID),
            zap.String("redis_key", redisKey))
        return c.Reply("Failed to temporarily store file information. Please try uploading again.")
    }

    h.logger.Info("Pending file info stored in Redis",
        zap.String("redis_key", redisKey),
        zap.Int64("user_id", userID))

    // Reply with inline keyboard for type selection
    return c.Reply(fmt.Sprintf("File '%s' validated. Please select its category:", sanitizedFileName), h.menu.fileTypeSelection)
}

// handleSelectFileType is the common logic for handling file type selection callbacks.
func (h *Handler) handleSelectFileType(c tele.Context, docType models.DocumentType) error {
    userID := c.Sender().ID
    redisKey := pendingFileKeyPrefix + strconv.FormatInt(userID, 10) // Ensure pendingFileKeyPrefix and strconv are available/imported

    var pendingInfo PendingFileInfo // Ensure PendingFileInfo is defined
    err := h.cache.Get(c.Request().Context(), redisKey, &pendingInfo)

    if err != nil {
        if err == redis.Nil { // Assuming 'redis' is the alias for 'github.com/redis/go-redis/v9'
            h.logger.Warn("Pending file info not found in Redis or expired", zap.Int64("user_id", userID), zap.String("redis_key", redisKey))
            // Respond to callback to remove "loading" state on client
            c.Respond(&tele.CallbackResponse{Text: "File selection timed out or data not found. Please upload again.", ShowAlert: true})
            return nil // Or an error if you prefer to log it higher up
        }
        h.logger.Error("Failed to retrieve pending file info from Redis", zap.Error(err), zap.Int64("user_id", userID))
        c.Respond(&tele.CallbackResponse{Text: "Error retrieving file info. Please try again.", ShowAlert: true})
        return err // Return the actual error for logging by the bot framework
    }

    // Crucial: Delete the key from Redis now that we have the data.
    delErr := h.cache.Del(c.Request().Context(), redisKey)
    if delErr != nil {
        // Log this error, as it might mean we reprocess if the user clicks again, but proceed with saving.
        h.logger.Error("Failed to delete pending file info from Redis after retrieval", zap.Error(delErr), zap.String("redis_key", redisKey))
    }

    h.logger.Info("Retrieved pending file info from Redis, proceeding to save",
        zap.String("redis_key", redisKey),
        zap.String("sanitized_name", pendingInfo.SanitizedFileName),
        zap.Int64("user_id", userID))
        
    // Call FileService to save the file.
    // Note: The FileService.SaveFile signature was updated to:
    // SaveFile(ctx context.Context, userID int64, docType models.DocumentType, fileData []byte, 
    //          sanitizedFileName, originalFileName string, fileSize int64, mimeType string) (*models.Document, error)
    _, saveErr := h.fileService.SaveFile(
        c.Request().Context(), 
        userID, 
        docType, 
        pendingInfo.FileData, 
        pendingInfo.SanitizedFileName,
        pendingInfo.OriginalFileName, 
        pendingInfo.FileSize, 
        pendingInfo.MIMEType,
    )

    if saveErr != nil {
        h.logger.Error("Failed to save file via FileService after type selection",
            zap.Error(saveErr),
            zap.Int64("user_id", userID),
            zap.String("sanitized_name", pendingInfo.SanitizedFileName),
            zap.String("doc_type", string(docType)))
        c.Respond(&tele.CallbackResponse{Text: fmt.Sprintf("Failed to save your file to %s. Please try again.", docType), ShowAlert: true})
        return saveErr
    }

    // Success. Edit the original message (where the inline keyboard was) to show confirmation.
    // The original message is c.Callback().Message
    if c.Callback() != nil && c.Callback().Message != nil {
        editedMessage := fmt.Sprintf("File '%s' successfully saved to %s!", pendingInfo.OriginalFileName, docType)
        _, editErr := h.bot.Edit(c.Callback().Message, editedMessage, &tele.SendOptions{}) // Remove keyboard by not passing a new one
        if editErr != nil {
            h.logger.Error("Failed to edit original message after file type selection", zap.Error(editErr))
            // Fallback reply if edit fails
            return c.Reply(editedMessage)
        }
    } else {
        // Fallback reply if original message can't be edited (should not happen with inline button callbacks)
        return c.Reply(fmt.Sprintf("File '%s' successfully saved to %s!", pendingInfo.OriginalFileName, docType))
    }
    
    return c.Respond() // Acknowledge the callback
}

// handleSelectTypeKnowledgeBase is called when the user selects "Knowledge Base" for a pending file.
func (h *Handler) handleSelectTypeKnowledgeBase(c tele.Context) error {
    return h.handleSelectFileType(c, models.KnowledgeBase)
}

// handleSelectTypePrivate is called when the user selects "Private" for a pending file.
func (h *Handler) handleSelectTypePrivate(c tele.Context) error {
    return h.handleSelectFileType(c, models.Private)
}

func (h *Handler) handleText(c tele.Context) error {
    userID := c.Sender().ID
    allowed, err := h.rateLimiter.Allow(c.Request().Context(), strconv.FormatInt(userID, 10))
    if err != nil {
        h.logger.Error("Rate limiter check failed for handleText", zap.Error(err), zap.Int64("user_id", userID))
        return c.Reply("Sorry, something went wrong. Please try again in a moment.")
    }
    if !allowed {
        h.logger.Info("Rate limit exceeded for handleText", zap.Int64("user_id", userID))
        return c.Reply("You are sending too many requests. Please wait a moment and try again.")
    }

    userID := c.Sender().ID // This was already here for rate limiter
    query := c.Text()

    h.logger.Info("handleText processing query",
        zap.Int64("user_id", userID),
        zap.String("query", query))

    // Call AIService.QueryDocuments
    // responseMsg will contain the AI's answer on success, or a user-friendly error message on failure.
    // internalErr is the actual error object for logging.
    responseMsg, internalErr := h.aiService.QueryDocuments(c.Request().Context(), userID, query)
    
    if internalErr != nil {
        h.logger.Error("Error from AIService.QueryDocuments",
            zap.Error(internalErr), // Log the actual internal error
            zap.Int64("user_id", userID),
            zap.String("user_facing_msg", responseMsg)) // Log the message that will be shown to user
        
        // Reply to the user with the user-facing message from AIService and the home menu.
        return c.Reply(responseMsg, h.menu.home) 
    }

    // Success case: responseMsg contains the AI's answer.
    h.logger.Info("Successfully received response from AIService.QueryDocuments",
        zap.String("response_preview", responseMsg[:min(100, len(responseMsg))]), // Log a preview
        zap.Int64("user_id", userID))

    // Reply to the user with the AI's answer and the home menu.
    return c.Reply(responseMsg, h.menu.home)
}

// min is a helper function to get the minimum of two integers.
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
