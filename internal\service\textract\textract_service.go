package textract

import (
	"fmt"
	// "bytes" // Removed as no longer used
	// "strings" // Removed as no longer used

	"go.uber.org/zap"

	// "github.com/unidoc/unioffice/v2/common/component" // Temporarily disabled
	// "github.com/unidoc/unioffice/v2/presentation" // Temporarily disabled
)

type TextExtractService struct {
	logger *zap.Logger
}

func NewTextExtractService(logger *zap.Logger) *TextExtractService {
	return &TextExtractService{logger: logger}
}

func (s *TextExtractService) ExtractText(fileData []byte, mimeType string) (string, error) {
	s.logger.Info("ExtractText called", zap.String("mimeType", mimeType))

	if mimeType == "application/vnd.openxmlformats-officedocument.presentationml.presentation" {
		s.logger.Info("PPTX MIME type detected, attempting extraction")
		s.logger.Warn("PPTX extraction is temporarily disabled due to library issues.")
		return "", fmt.Errorf("PPTX extraction is temporarily disabled")
		// reader := bytes.NewReader(fileData)
		// prs, err := presentation.Read(reader, int64(len(fileData)))
		// if err != nil {
		// 	s.logger.Error("Failed to open PPTX data", zap.Error(err))
		// 	return "", fmt.Errorf("error opening PPTX data: %w", err)
		// }
		// // According to unioffice examples, Close is typically used when opening from a file path.
		// // When Read from a reader, it's less common to see an explicit Close,
		// // but good practice to check if the specific type (prs) implements io.Closer if resource leaks are a concern.
		// // For now, assuming direct Read handles its resources.

		// var textBuilder strings.Builder
		// for i, slide := range prs.Slides() {
		// 	s.logger.Debug("Processing slide", zap.Int("slideIndex", i))
		// 	for _, content := range slide.Content() {
		// 		if drawingObject, ok := content.(component.DrawingObject); ok {
		// 			for _, para := range drawingObject.Paragraphs() {
		// 				for _, run := range para.Runs() {
		// 					textBuilder.WriteString(run.Text())
		// 				}
		// 				textBuilder.WriteString("\n") // Add a newline between paragraphs
		// 			}
		// 		}
		// 	}
		// }

		// extractedText := textBuilder.String()
		// s.logger.Info("Successfully extracted text from PPTX", zap.Int("textLength", len(extractedText)))
		// return extractedText, nil
	}

	s.logger.Warn("Unsupported MIME type for text extraction", zap.String("mimeType", mimeType))
	// Maintain existing behavior: log a warning and return "", nil for non-PPTX types.
	// For consistency, let's make unsupported also an error, or ensure disabled PPTX is not an error.
	// For now, returning nil to match existing behavior for other unsupported types.
	return "", nil
}
