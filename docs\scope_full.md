## Updated Project Scope: NoorBot Telegram AI Assistant

### Objective
Develop **NoorBot**, a Telegram-based AI assistant built in Golang, integrated with PostgreSQL (using the pgvector extension), and optimized for performance, scalability, and privacy. NoorBot will manage two distinct types of uploaded documents—knowledge base files for AI enhancement and long time memory. and private files for personal use—while delivering a seamless, button-driven interface with a strong focus on UI/UX.

---

## Key Features with Enhancements

### 1. Advanced Document Management
- **File Storage**:  
  - Documents are stored securely in a user-specific folder structure:  
    - Knowledge Base: `/uploads/knowledge_base/[user_id]/[filename]`  
    - Private Files: `/uploads/private/[user_id]/[filename]`  
  - **New Enhancement**: Add subfolder support for organization (e.g., `/uploads/private/[user_id]/travel/passport.pdf`).

- **File Upload Process**:  
  - Initiate with using telegram attachment icon button.  
  - Select file type via inline buttons: `[Knowledge Base]` or `[Private]`.  
  - Option to create or select a subfolder (e.g., `[New Folder]`, `[Existing: Travel]`).  
  - Supported formats: `.pdf`, `.txt`, `.docx`, with a maximum file size limit (e.g., 150 MB).  
  - Feedback: "File 'passport.pdf' saved to Private/Travel successfully!"

- **File Actions**:  
  - **List Files**: Access via `[My Files]`, with options `[Private]` or `[Knowledge Base]` and folder navigation (e.g., `[Travel]`, `[Back]`).  
  - **Delete**: `[Delete]` with confirmation (`[Yes]`, `[No]`).  
  - **Download**: `[Download]` for private files, delivered via Telegram.  
  - `[Rename]` button to edit filenames and `[Move]` to shift files between folders or types.  
  - "[My Files]' will retieve the all files in different folders.
  - the list of files to be displayed in the list will be limited to 10 files per page.
  -  the titles of listed files should start from left regardless the languages and with numbering.t the full title should be displayed in box container.
  - at the riight of the title box there should be drop down icon which will open inline buttons for [Delete], [Download], [Rename], and [Move].
    


---

### 2. Two Types of Uploaded Files
- **Knowledge Base Files**:  
  - Purpose: Enhance NoorBot's AI responses for all interactions.  
  - Usage: Converted to embeddings and stored in pgvector for context-aware answers.  
  - Management: List, delete, or move via `[My Files]`.  

- **Private Files**:  
  - Purpose: Store personal documents securely (e.g., passports, notes).  
  - Usage: Accessible only by the user's AI queries (e.g., "What's my passport number?").  
  - Management: List, delete, download, rename, or move via `[My Files]`.  
  - **Privacy**: Isolated to the user's `[user_id]` folder; not used in the general AI knowledge base.

---

### 3. AI Interaction with Private Files
- **Functionality**:  
 - i can make normal chat with the AI in the telegram chat box.
 - AI can access the files under the Knowldge to give me any details related to stored files.
  - Query via `[Ask]` (e.g., "What's my address?"). NoorBot scans private files temporarily to respond.  
  -   
- **Privacy Assurance**:  
  - Private file content is processed locally, not stored remotely, and wiped from memory after the query.  

---

### 4. Enhanced Technical Features

#### AI and Embedding
- **AI Backend**:  
  - Integrates DeepSeek AI for natural language responses.  
  - **New Enhancement**: Fallback to a secondary AI model (e.g., OpenAI) if DeepSeek is unavailable.  
- **Embedding Generation**:  
  - Uses **Sentence Transformers** (`all-MiniLM-L6-v2`) for local embeddings.  and that to be running in the background and should not imact the user experience.
  - Support for multi-language embeddings with automatic language detection.  
  - Auto-downloads model on first run; updates checked periodically in the background.  
- **Optimization**:  
  -  Implement a hybrid search combining vector similarity and keyword matching for more accurate responses.  
  - Cache embeddings and responses in **Redis** with a TTL (e.g., 24 hours) to boost performance.

#### Performance and Scalability
- **Asynchronous Processing**:  
  - Goroutines handle file uploads, embedding generation, and AI queries concurrently.  
  -  Priority queue for critical tasks (e.g., small file uploads over large embeddings).  
- **Rate Limiting**:  
  - Enforce per-user limits (e.g., 15 requests/minute) with a dynamic cap based on server load.  
- **Scalability**:  
  -Containerize with Docker for easy scaling across multiple instances.  
  - Load balancing via **Nginx** or a similar reverse proxy.  
- **Monitoring**:  
  - Use **Prometheus** and **Grafana** for real-time metrics (e.g., query latency, upload failures).  

#### Security (optional to be added at later stage)
- **Encryption**:  
  - Files encrypted at rest with **AES-256**; keys managed via a secure key vault (e.g., HashiCorp Vault).  
  - Transit encryption via **TLS 1.3**.  
- **User Isolation**:  
  - Enforce `userID` checks at every database and file access point.  


---

### 5. Enhanced UI/UX Features

#### Button-Driven Interface
- **Persistent Keyboard**:  
  - `[Home]`, `[My Files]`, `[Ask]`, `[Search]`, `[Help]`.  
- **Inline Buttons**:  
  - Uploa/downloadd: `[Knowledge Base]`, `[Private]`, `[New Folder]`.  
  - File actions: `[Delete]`, `[Download]`, `[Rename]`, `[Move]`.  
  

#### Guided User Flows
- **Onboarding**:  
  - Welcome message with `[Get Started]` button or when the bot is restarted, offering a quick tour (e.g., "Try uploading a file!").  
- **Multi-Step Interactions**:  
  - Upload: Step 1: Select file → Step 2: Choose type → Step 3: Pick folder (optional).  
  - **New Enhancement**: Add `[Cancel]` at any step to abort gracefully.  
- **Progress Indicators**:  
  - "Uploading… (50%)" or "Generating embeddings…" with animated dots for long tasks.

#### User-Friendly Feedback
- **Success Messages**:  
  - "File 'notes.txt' moved to Private/Notes!"  
- **Error Handling**:  
  - "File too large! Max size is 50 MB. Try compressing it."  
- **Help System**:  
  - `[Help]` offers a searchable FAQ (e.g., "How do I upload a file?") with examples.  

---

### Technical Implementation
- **Database**:  
  - PostgreSQL with pgvector for vector storage.  
  -use best practice for indexinf for example,Tables:  
    - `files`: `file_id`, `user_id`, `file_type`, `file_path`, `file_name`, `upload_time`, `folder`.  
    - `audit_logs`: `log_id`, `user_id`, `action`, `timestamp`.  
- **AI Integration**:  
  - DeepSeek AI with fallback; Sentence Transformers for embeddings.  
- **Caching**:  
  - Redis for query and embedding caching.  
- **Deployment**:  
  - Dockerized on a private server (128 GB RAM, CPU, 400 GB disk).  

---

### How It Works for You
1. **Upload a File**:  
   - Click Attchemnt icon, choose `[Private]`, select `[Travel]` folder, upload `passport.pdf`.  
   - Response: "File saved to Private/Travel!"  
2. **Manage Files**:  
   - `[My Files]` → `[Private]` → `[Travel]` → `[passport.pdf]` with `[Delete]`, `[Download]`, `[Rename]`, `[Move]`.  
3.

### Summary of Enhancements
- **Technical**:  
  - Multi-language embeddings, hybrid search, Docker scalability, 2FA, audit logging.  
- **UI/UX**:  
  - Folder support, rename/move actions, onboarding tour, cancel options, custom themes.  