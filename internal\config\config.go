package config

import (
	"fmt"
	"github.com/joho/godotenv"
	"os"
	"strconv"
	"strings"
)

type Config struct {
	Telegram struct {
		Token        string
		AllowedUsers []int64
	}
	DB struct {
		Host     string
		Port     string
		User     string
		Password string
		DBName   string
	}
	Redis struct {
		Host     string
		Password string
		DB       int
	}
	Storage struct {
		Path string
	}
	AI AIConfig // Changed to use the new exported type
	Server struct {
		Port string
	}
}

// AIConfig holds all AI related configurations
type AIConfig struct {
	DeepSeekToken       string
	ModelPath           string
	DeepSeekModel       string
	DeepSeekTemperature float32
	DeepSeekMaxTokens   int
	// Removed extra closing brace here
}

func Load() (*Config, error) {
	// Make .env file loading optional
	godotenv.Load() // Ignore error if file doesn't exist

	cfg := &Config{}
	
	// Load Telegram config
	cfg.Telegram.Token = os.Getenv("TELEGRAM_TOKEN")
	if cfg.Telegram.Token == "" {
		return nil, fmt.Errorf("TELEGRAM_TOKEN is required")
	}

	// Parse allowed users
	if allowedUsersStr := os.Getenv("ALLOWED_USERS"); allowedUsersStr != "" {
		for _, userStr := range strings.Split(allowedUsersStr, ",") {
			userID, err := strconv.ParseInt(strings.TrimSpace(userStr), 10, 64)
			if err != nil {
				return nil, fmt.Errorf("invalid ALLOWED_USERS format: %w", err)
			}
			cfg.Telegram.AllowedUsers = append(cfg.Telegram.AllowedUsers, userID)
		}
	}

	// Load DB config
	cfg.DB.Host = os.Getenv("DB_HOST")
	cfg.DB.Port = os.Getenv("DB_PORT")
	cfg.DB.User = os.Getenv("DB_USER")
	cfg.DB.Password = os.Getenv("DB_PASSWORD")
	cfg.DB.DBName = os.Getenv("DB_NAME")

	// Load Redis config
	cfg.Redis.Host = os.Getenv("REDIS_HOST")
	cfg.Redis.Password = os.Getenv("REDIS_PASSWORD")
	cfg.Redis.DB = 0 // Default to DB 0

	// Load Storage config
	cfg.Storage.Path = os.Getenv("STORAGE_PATH")
	if cfg.Storage.Path == "" {
		cfg.Storage.Path = "./storage" // Default value
	}

	// Load AI config
	cfg.AI.DeepSeekToken = os.Getenv("DEEPSEEK_TOKEN")
	// Note: DEEPSEEK_TOKEN should be checked for emptiness if it's critical for DeepSeekClient to function.
	// For now, assuming it can be empty if DeepSeekClient is not used or handles it.

	cfg.AI.ModelPath = os.Getenv("MODEL_PATH") // This seems unrelated to DeepSeek, possibly for local models

	cfg.AI.DeepSeekModel = os.Getenv("DEEPSEEK_MODEL")
	if cfg.AI.DeepSeekModel == "" {
		cfg.AI.DeepSeekModel = "deepseek-chat" // Default value
	}

	tempStr := os.Getenv("DEEPSEEK_TEMPERATURE")
	if tempStr == "" {
		cfg.AI.DeepSeekTemperature = 0.7 // Default value
	} else {
		tempF64, err := strconv.ParseFloat(tempStr, 32)
		if err != nil {
			// Log or handle error? For now, use default.
			cfg.AI.DeepSeekTemperature = 0.7
		} else {
			cfg.AI.DeepSeekTemperature = float32(tempF64)
		}
	}

	maxTokensStr := os.Getenv("DEEPSEEK_MAX_TOKENS")
	if maxTokensStr == "" {
		cfg.AI.DeepSeekMaxTokens = 1024 // Default value
	} else {
		maxTokensInt, err := strconv.Atoi(maxTokensStr)
		if err != nil {
			// Log or handle error? For now, use default.
			cfg.AI.DeepSeekMaxTokens = 1024
		} else {
			cfg.AI.DeepSeekMaxTokens = maxTokensInt
		}
	}

	// Load Server config
	cfg.Server.Port = os.Getenv("APP_PORT")
	if cfg.Server.Port == "" {
		cfg.Server.Port = "8081" // default port
	}

	return cfg, nil
}

func (c *Config) GetDBConnString() string {
	return fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
		c.DB.User, c.DB.Password, c.DB.Host, c.DB.Port, c.DB.DBName)
}
