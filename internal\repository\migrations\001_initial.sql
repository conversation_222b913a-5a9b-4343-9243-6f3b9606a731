-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username <PERSON><PERSON><PERSON><PERSON>(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(255),
    last_name <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_active TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    documents_count INTEGER DEFAULT 0,
    storage_used BIGINT DEFAULT 0,
    storage_limit BIGINT DEFAULT 1073741824, -- 1GB default
    is_active BOOLEAN DEFAULT true
);

-- Documents table
CREATE TABLE documents (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    type VARCHAR(20) NOT NULL,
    filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    path TEXT NOT NULL,
    folder VA<PERSON>HAR(255),
    size BIGINT NOT NULL,
    upload_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_access TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    embedding vector(384),
    is_processed BOOLEAN DEFAULT false,
    
    CONSTRAINT valid_type CHECK (type IN ('knowledge_base', 'private'))
);

-- Indexes
CREATE INDEX idx_documents_user_type ON documents(user_id, type);
CREATE INDEX idx_documents_folder ON documents(folder);
CREATE INDEX idx_documents_embedding ON documents USING ivfflat (embedding vector_cosine_ops);

-- Audit logs table
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    action VARCHAR(50) NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
