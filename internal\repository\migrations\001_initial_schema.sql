package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"noorbotv2/internal/models"
)

type UserRepository struct {
	db *pgx.Conn
}

func NewUserRepository(db *pgx.Conn) *UserRepository {
	return &UserRepository{db: db}
}

func (r *UserRepository) CreateUser(ctx context.Context, user *models.User) error {
	query := `
		INSERT INTO users (telegram_id, created_at, updated_at)
		VALUES ($1, $2, $2)
		RETURNING id`
	
	now := time.Now()
	return r.db.QueryRow(ctx, query,
		user.TelegramID, now,
	).Scan(&user.ID)
}

func (r *UserRepository) GetUserByTelegramID(ctx context.Context, telegramID int64) (*models.User, error) {
	query := `
		SELECT id, telegram_id, created_at, updated_at
		FROM users
		WHERE telegram_id = $1`

	user := &models.User{}
	err := r.db.QueryRow(ctx, query, telegramID).Scan(
		&user.ID, &user.TelegramID, &user.CreatedAt, &user.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return user, nil
}
