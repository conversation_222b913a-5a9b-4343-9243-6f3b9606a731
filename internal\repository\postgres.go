package repository

import (
    "context"
    "fmt"
    "time"
    
    "github.com/jackc/pgx/v5"
    "noorbotv2/internal/models"
)

type PostgresRepository struct {
    db *pgx.Conn
}

func NewPostgresRepository(connStr string) (*PostgresRepository, error) {
    ctx := context.Background()
    conn, err := pgx.Connect(ctx, connStr)
    if err != nil {
        return nil, fmt.Errorf("unable to connect to database: %w", err)
    }

    // Test connection
    if err := conn.Ping(ctx); err != nil {
        return nil, fmt.Errorf("unable to ping database: %w", err)
    }
    
    return &PostgresRepository{db: conn}, nil
}

func (r *PostgresRepository) SaveDocument(ctx context.Context, doc *models.Document) error {
    query := `
        INSERT INTO documents (user_id, name, type, path, folder, size, embedding, created_at, updated_at, mime_type)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id`
    
    err := r.db.QueryRow(ctx, query,
        doc.UserID, doc.Name, doc.Type, doc.Path, doc.Folder, doc.Size, doc.Embedding,
        time.Now(), time.Now(), doc.MIMEType, // Added doc.MIMEType
    ).Scan(&doc.ID)
    
    if err != nil {
        return fmt.Errorf("failed to save document: %w", err)
    }
    
    return nil
}

func (r *PostgresRepository) GetDocuments(ctx context.Context, userID int64, docType models.DocumentType) ([]*models.Document, error) {
    query := `
        SELECT id, user_id, name, type, path, folder, size, embedding, created_at, updated_at, mime_type
        FROM documents
        WHERE user_id = $1 AND type = $2
        ORDER BY created_at DESC`

    rows, err := r.db.Query(ctx, query, userID, docType)
    if err != nil {
        return nil, fmt.Errorf("failed to query documents: %w", err)
    }
    defer rows.Close()

    var docs []*models.Document
    for rows.Next() {
        doc := &models.Document{}
        err := rows.Scan(
            &doc.ID, &doc.UserID, &doc.Name, &doc.Type, &doc.Path, &doc.Folder,
            &doc.Size, &doc.Embedding, &doc.CreatedAt, &doc.UpdatedAt, &doc.MIMEType, // Added &doc.MIMEType
        )
        if err != nil {
            return nil, fmt.Errorf("failed to scan document: %w", err)
        }
        docs = append(docs, doc)
    }

    return docs, nil
}

func (r *PostgresRepository) Close(ctx context.Context) error {
    return r.db.Close(ctx)
}

func (r *PostgresRepository) Health(ctx context.Context) error {
    return r.db.Ping(ctx)
}

// SearchSimilarDocuments finds documents similar to the query embedding for a user.
// docType can be nil to search across all document types for the user.
func (r *PostgresRepository) SearchSimilarDocuments(ctx context.Context, userID int64, queryEmbedding []float32, limit int, docType *models.DocumentType) ([]*models.Document, error) {
    if queryEmbedding == nil {
        return nil, fmt.Errorf("queryEmbedding cannot be nil")
    }
    if limit <= 0 {
        limit = 5 // Default limit
    }

    var rows pgx.Rows
    var err error

    // Base query structure. Parameters will be added dynamically.
    // $1 will be userID, $2 will be queryEmbedding
    baseQueryParts := []string{
        "SELECT id, user_id, name, type, path, folder, size, embedding, created_at, updated_at, mime_type, embedding <=> $2 AS distance",
        "FROM documents",
        "WHERE user_id = $1",
    }

    params := []interface{}{userID, queryEmbedding}
    paramIdx := 2 // Current parameter index, $1=userID, $2=queryEmbedding

    if docType != nil {
        paramIdx++
        baseQueryParts = append(baseQueryParts, fmt.Sprintf("AND type = $%d", paramIdx))
        params = append(params, *docType)
    }

    // ORDER BY and LIMIT clauses
    // The embedding parameter for ORDER BY is already $2
    paramIdx++
    baseQueryParts = append(baseQueryParts, fmt.Sprintf("ORDER BY distance LIMIT $%d", paramIdx))
    params = append(params, limit)

    finalQuery := ""
    for _, part := range baseQueryParts {
        finalQuery += part + " "
    }
    
    r.db.Logger().Log(ctx, pgx.LogLevelInfo, "Executing SearchSimilarDocuments query", 
        map[string]interface{}{"query": finalQuery, "userID": userID, "limit": limit, "docType": docType, "params_count": len(params)})

    rows, err = r.db.Query(ctx, finalQuery, params...)

    if err != nil {
        return nil, fmt.Errorf("failed to query similar documents: %w", err)
    }
    defer rows.Close()

    var docs []*models.Document
    for rows.Next() {
        doc := &models.Document{}
        var distance float32 // To scan the distance, not part of models.Document but useful for logging/debugging
        err := rows.Scan(
            &doc.ID, &doc.UserID, &doc.Name, &doc.Type, &doc.Path, &doc.Folder,
            &doc.Size, &doc.Embedding, &doc.CreatedAt, &doc.UpdatedAt,
            &doc.MIMEType, // Added &doc.MIMEType
            &distance,    // Scan the distance
        )
        if err != nil {
            r.db.Logger().Log(ctx, pgx.LogLevelError, "failed to scan similar document", map[string]interface{}{"error": err})
            // Decide if one bad scan should fail all. For now, continue and log.
            continue 
        }
        // Optionally log distance: 
        // r.db.Logger().Log(ctx, pgx.LogLevelDebug, "Found document", map[string]interface{}{"name": doc.Name, "distance": distance})
        docs = append(docs, doc)
    }
    if rows.Err() != nil {
         return nil, fmt.Errorf("error iterating similar document rows: %w", rows.Err())
    }

    return docs, nil
}
