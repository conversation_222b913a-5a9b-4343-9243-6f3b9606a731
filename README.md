# NoorBot

A Telegram bot for AI-powered document assistance, built with Go and Clean Architecture.

## Features

- Document upload and processing
- AI-powered document analysis
- Vector similarity search
- Interactive Telegram interface

## Prerequisites

- Go 1.22+
- Docker and Docker Compose
- PostgreSQL with pgvector extension (running on port 5434)
- Telegram Bot Token

## Configuration

The default PostgreSQL port is 5434. If you need to change this, update the following files:
- `.env`
- `docker-compose.yml`
- CI configuration

## Quick Start

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/noorbot.git
   cd noorbot
   ```

2. Set up environment:
   ```bash
   cp .env.example .env
   # Edit .env with your values
   ```

3. Start services:
   ```bash
   docker-compose up -d
   ```

4. Run the bot:
   ```bash
   go run cmd/bot/main.go
   ```

## Development

### Project Structure

```
noorbot/
├── cmd/                 # Application entrypoints
├── internal/           # Private application code
│   ├── handler/       # Request handlers
│   ├── repository/    # Data access layer
│   └── service/       # Business logic
├── pkg/               # Public libraries
└── scripts/           # Utility scripts
```

### Testing

Run all tests:
```bash
go test ./...
```

Run database tests:
```bash
./scripts/test_db.sh
```

### Health Checks

The service exposes health check endpoints:

- `GET /health` - Overall service health
  ```json
  {
    "status": "ok",
    "database": "ok",
    "timestamp": "2024-03-14T12:00:00Z"
  }
  ```

## Deployment

The service uses GitHub Actions for CI/CD:

1. Tests run on every push and PR
2. Automatic deployment on merge to main
3. Coverage reports via codecov

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

[MIT License](LICENSE)
