# NoorBot Simplified Project Structure

## Directory Layout

```
noorbot/
├── cmd/
│   └── bot/
│       └── main.go               # Application entry point
│
├── internal/
│   ├── models/
│   │   ├── user.go              # User model
│   │   └── document.go          # Document model
│   │
│   ├── repository/
│   │   ├── postgres.go          # PostgreSQL operations
│   │   ├── redis_cache.go       # Redis caching
│   │   └── migrations/          # Database migrations
│   │       └── 001_initial.sql
│   │
│   ├── service/
│   │   ├── file_service.go      # File operations
│   │   ├── ai_service.go        # AI & embeddings
│   │   └── bot_service.go       # Telegram bot logic
│   │
│   └── handler/
│       └── telegram/
│           ├── commands.go       # Bot commands
│           ├── buttons.go        # UI buttons
│           └── messages.go       # Message handling
│
├── uploads/                      # File storage
│   ├── knowledge_base/
│   │   └── [user_id]/
│   └── private/
│       └── [user_id]/
│
├── docs/
│   ├── scope_full.md            # Project scope
│   └── project_structure.md     # This file
│
├── .env.example                 # Environment template
├── .gitignore
├── Dockerfile
├── docker-compose.yml
├── go.mod
└── README.md
```

## Key Components

### 1. Core Components

#### Models (`internal/models/`)
- `user.go`: User information
- `document.go`: File metadata and embeddings

#### Repository (`internal/repository/`)
- PostgreSQL with pgvector
- Redis caching
- Database migrations

#### Services (`internal/service/`)
- File handling
- AI integration (DeepSeek)
- Telegram bot operations

#### Handlers (`internal/handler/telegram/`)
- Command processing
- Button interactions
- Message handling

### 2. File Organization (`uploads/`)
- Knowledge base files
- Private documents
- User-specific folders

### 3. Configuration
- Environment variables
- Docker setup
- Database settings

## Main Features Implementation

### 1. Document Management
- Upload/download handling
- Folder organization
- File type separation

### 2. AI Integration
- DeepSeek integration
- Embedding generation
- Vector similarity search

### 3. User Interface
- Button-driven commands
- File management actions
- Progress indicators

### 4. Security
- User isolation
- File access control
- Input validation

## Development Guidelines

### 1. Code Organization
- Keep services focused
- Use clean architecture
- Maintain separation of concerns

### 2. Configuration
- Use `.env` for settings
- Document all variables
- Secure sensitive data

### 3. Testing
- Write unit tests
- Test file operations
- Verify AI integration

### 4. Deployment
- Use Docker
- Monitor performance
- Handle errors gracefully