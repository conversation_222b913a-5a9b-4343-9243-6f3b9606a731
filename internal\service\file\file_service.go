package file

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"noorbotv2/internal/models"
	"noorbotv2/internal/repository"

	"go.uber.org/zap"
)

// TextExtractService interface for text extraction (to be implemented later)
type TextExtractService interface {
	ExtractText(fileData []byte, mimeType string) (string, error)
}

type Service struct {
	repo            repository.DocumentRepository
	path            string
	logger          *zap.Logger
	textractService TextExtractService
}

func NewFileService(
	repo repository.DocumentRepository,
	path string,
	logger *zap.Logger,
	textractService TextExtractService,
) *Service {
	return &Service{
		repo:            repo,
		path:            path,
		logger:          logger,
		textractService: textractService,
	}
}

// SaveFile saves a file to the filesystem and database
func (s *Service) SaveFile(
	ctx context.Context,
	userID int64,
	docType models.DocumentType,
	fileData []byte,
	sanitizedFileName, originalFileName string,
	fileSize int64,
	mimeType string,
) (*models.Document, error) {
	s.logger.Info("Starting file save operation",
		zap.Int64("user_id", userID),
		zap.String("doc_type", string(docType)),
		zap.String("filename", sanitizedFileName),
		zap.Int64("size", fileSize))

	// Create user directory structure
	userDir := filepath.Join(s.path, string(docType), fmt.Sprintf("%d", userID))
	if err := os.MkdirAll(userDir, 0755); err != nil {
		s.logger.Error("Failed to create user directory", zap.String("dir", userDir), zap.Error(err))
		return nil, fmt.Errorf("failed to create user directory: %w", err)
	}

	// Write file to filesystem
	filePath := filepath.Join(userDir, sanitizedFileName)
	if err := os.WriteFile(filePath, fileData, 0644); err != nil {
		s.logger.Error("Failed to write file to filesystem", zap.String("path", filePath), zap.Error(err))
		return nil, fmt.Errorf("failed to write file: %w", err)
	}

	// Create document model
	doc := &models.Document{
		UserID:    userID,
		Name:      originalFileName,
		Type:      docType,
		Path:      filePath,
		Folder:    "", // Default to root folder
		MIMEType:  mimeType,
		Size:      fileSize,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save to database
	if err := s.repo.SaveDocument(ctx, doc); err != nil {
		// Clean up file if database save fails
		os.Remove(filePath)
		s.logger.Error("Failed to save document to database", zap.Error(err))
		return nil, fmt.Errorf("failed to save document to database: %w", err)
	}

	s.logger.Info("File saved successfully",
		zap.Int64("doc_id", doc.ID),
		zap.String("path", filePath))

	return doc, nil
}

// ListFilesByType retrieves files by type for a user with folder support
func (s *Service) ListFilesByType(ctx context.Context, userID int64, docType models.DocumentType) ([]*models.Document, error) {
	s.logger.Info("Listing files by type",
		zap.Int64("user_id", userID),
		zap.String("doc_type", string(docType)))

	docs, err := s.repo.GetDocuments(ctx, userID, docType)
	if err != nil {
		s.logger.Error("Failed to retrieve documents from database", zap.Error(err))
		return nil, fmt.Errorf("failed to retrieve documents: %w", err)
	}

	s.logger.Info("Retrieved documents successfully",
		zap.Int("count", len(docs)))

	return docs, nil
}

// DeleteFile removes a file from both filesystem and database
func (s *Service) DeleteFile(ctx context.Context, userID int64, documentID int64) error {
	s.logger.Info("Starting file deletion",
		zap.Int64("user_id", userID),
		zap.Int64("document_id", documentID))

	// Get document info first
	doc, err := s.repo.GetDocumentByID(ctx, userID, documentID)
	if err != nil {
		s.logger.Error("Failed to get document for deletion", zap.Error(err))
		return fmt.Errorf("failed to get document: %w", err)
	}

	// Delete from filesystem
	if err := os.Remove(doc.Path); err != nil && !os.IsNotExist(err) {
		s.logger.Warn("Failed to delete file from filesystem", zap.String("path", doc.Path), zap.Error(err))
		// Continue with database deletion even if file deletion fails
	}

	// Delete from database
	if err := s.repo.DeleteDocument(ctx, userID, documentID); err != nil {
		s.logger.Error("Failed to delete document from database", zap.Error(err))
		return fmt.Errorf("failed to delete document from database: %w", err)
	}

	s.logger.Info("File deleted successfully",
		zap.Int64("document_id", documentID),
		zap.String("path", doc.Path))

	return nil
}

// RenameFile renames a file in both filesystem and database
func (s *Service) RenameFile(ctx context.Context, userID int64, documentID int64, newName string) error {
	s.logger.Info("Starting file rename",
		zap.Int64("user_id", userID),
		zap.Int64("document_id", documentID),
		zap.String("new_name", newName))

	// Validate new name
	if strings.TrimSpace(newName) == "" {
		return fmt.Errorf("new filename cannot be empty")
	}

	// Get document info
	doc, err := s.repo.GetDocumentByID(ctx, userID, documentID)
	if err != nil {
		s.logger.Error("Failed to get document for rename", zap.Error(err))
		return fmt.Errorf("failed to get document: %w", err)
	}

	// Generate new file path
	dir := filepath.Dir(doc.Path)
	ext := filepath.Ext(doc.Name)
	if !strings.HasSuffix(newName, ext) {
		newName += ext // Preserve original extension
	}
	newPath := filepath.Join(dir, newName)

	// Rename file in filesystem
	if err := os.Rename(doc.Path, newPath); err != nil {
		s.logger.Error("Failed to rename file in filesystem", zap.Error(err))
		return fmt.Errorf("failed to rename file: %w", err)
	}

	// Update database
	if err := s.repo.UpdateDocumentName(ctx, userID, documentID, newName, newPath); err != nil {
		// Rollback filesystem change
		os.Rename(newPath, doc.Path)
		s.logger.Error("Failed to update document name in database", zap.Error(err))
		return fmt.Errorf("failed to update document name: %w", err)
	}

	s.logger.Info("File renamed successfully",
		zap.String("old_name", doc.Name),
		zap.String("new_name", newName))

	return nil
}

// MoveFile moves a file to a different folder or type
func (s *Service) MoveFile(ctx context.Context, userID int64, documentID int64, newType models.DocumentType, newFolder string) error {
	s.logger.Info("Starting file move",
		zap.Int64("user_id", userID),
		zap.Int64("document_id", documentID),
		zap.String("new_type", string(newType)),
		zap.String("new_folder", newFolder))

	// Get document info
	doc, err := s.repo.GetDocumentByID(ctx, userID, documentID)
	if err != nil {
		s.logger.Error("Failed to get document for move", zap.Error(err))
		return fmt.Errorf("failed to get document: %w", err)
	}

	// Create new directory structure
	newUserDir := filepath.Join(s.path, string(newType), fmt.Sprintf("%d", userID))
	if newFolder != "" {
		newUserDir = filepath.Join(newUserDir, newFolder)
	}
	if err := os.MkdirAll(newUserDir, 0755); err != nil {
		s.logger.Error("Failed to create new directory", zap.String("dir", newUserDir), zap.Error(err))
		return fmt.Errorf("failed to create new directory: %w", err)
	}

	// Generate new file path
	fileName := filepath.Base(doc.Path)
	newPath := filepath.Join(newUserDir, fileName)

	// Move file in filesystem
	if err := os.Rename(doc.Path, newPath); err != nil {
		s.logger.Error("Failed to move file in filesystem", zap.Error(err))
		return fmt.Errorf("failed to move file: %w", err)
	}

	// Update database
	if err := s.repo.UpdateDocumentLocation(ctx, userID, documentID, newType, newFolder, newPath); err != nil {
		// Rollback filesystem change
		os.Rename(newPath, doc.Path)
		s.logger.Error("Failed to update document location in database", zap.Error(err))
		return fmt.Errorf("failed to update document location: %w", err)
	}

	s.logger.Info("File moved successfully",
		zap.String("old_path", doc.Path),
		zap.String("new_path", newPath))

	return nil
}

// DownloadFile retrieves file data for download
func (s *Service) DownloadFile(ctx context.Context, userID int64, documentID int64) ([]byte, string, error) {
	s.logger.Info("Starting file download",
		zap.Int64("user_id", userID),
		zap.Int64("document_id", documentID))

	// Get document info
	doc, err := s.repo.GetDocumentByID(ctx, userID, documentID)
	if err != nil {
		s.logger.Error("Failed to get document for download", zap.Error(err))
		return nil, "", fmt.Errorf("failed to get document: %w", err)
	}

	// Read file data
	fileData, err := os.ReadFile(doc.Path)
	if err != nil {
		s.logger.Error("Failed to read file for download", zap.String("path", doc.Path), zap.Error(err))
		return nil, "", fmt.Errorf("failed to read file: %w", err)
	}

	s.logger.Info("File downloaded successfully",
		zap.String("filename", doc.Name),
		zap.Int("size", len(fileData)))

	return fileData, doc.Name, nil
}

func (s *Service) ExtractTextFromFile(fileName string, mimeType string) (string, error) {
	s.logger.Info("Attempting to extract text from file", zap.String("fileName", fileName), zap.String("mimeType", mimeType))

	// TODO: Implement text extraction when textract service is available
	if s.textractService == nil {
		s.logger.Warn("TextExtract service not available, returning empty text")
		return "", nil
	}

	fullPath := filepath.Join(s.path, fileName)

	fileData, err := os.ReadFile(fullPath)
	if err != nil {
		s.logger.Error("Failed to read file for text extraction", zap.String("path", fullPath), zap.Error(err))
		return "", fmt.Errorf("failed to read file %s: %w", fileName, err)
	}

	extractedText, err := s.textractService.ExtractText(fileData, mimeType)
	if err != nil {
		s.logger.Error("Text extraction failed", zap.String("fileName", fileName), zap.Error(err))
		return "", err
	}

	s.logger.Info("Successfully extracted text from file", zap.String("fileName", fileName), zap.Int("textLength", len(extractedText)))
	return extractedText, nil
}
