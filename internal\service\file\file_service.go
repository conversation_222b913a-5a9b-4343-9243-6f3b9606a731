package file

import (
	"fmt"
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"noorbotv2/internal/repository"
	"noorbotv2/internal/service/textract"
)

type Service struct {
	repo            *repository.PostgresRepository
	path            string
	logger          *zap.Logger
	textractService *textract.TextExtractService // New field
}

func NewFileService(
	repo *repository.PostgresRepository,
	path string,
	logger *zap.Logger,
	textractService *textract.TextExtractService, // New parameter
) *Service {
	return &Service{
		repo:            repo,
		path:            path,
		logger:          logger,
		textractService: textractService, // Assign new field
	}
}

func (s *Service) ExtractTextFromFile(fileName string, mimeType string) (string, error) {
	s.logger.Info("Attempting to extract text from file", zap.String("fileName", fileName), zap.String("mimeType", mimeType))

	fullPath := filepath.Join(s.path, fileName)

	fileData, err := os.ReadFile(fullPath)
	if err != nil {
		s.logger.Error("Failed to read file for text extraction", zap.String("path", fullPath), zap.Error(err))
		return "", fmt.Errorf("failed to read file %s: %w", fileName, err)
	}

	extractedText, err := s.textractService.ExtractText(fileData, mimeType)
	if err != nil {
		// Log the error from textractService but return it directly as it should be descriptive enough.
		s.logger.Error("Text extraction failed", zap.String("fileName", fileName), zap.Error(err))
		return "", err // Return the error from TextExtractService directly
	}

	s.logger.Info("Successfully extracted text from file", zap.String("fileName", fileName), zap.Int("textLength", len(extractedText)))
	return extractedText, nil
}
