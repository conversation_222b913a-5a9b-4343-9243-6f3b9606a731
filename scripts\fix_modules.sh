#!/bin/bash
set -e

# Function to check if modules need updating
check_modules_update() {
    if ! go mod verify &>/dev/null; then
        return 0 # needs update
    fi
    if ! go mod tidy -v 2>&1 | grep -q "unused module"; then
        return 1 # no update needed
    fi
    return 0 # needs update
}

echo "🔍 Checking if updates are needed..."
if ! check_modules_update; then
    echo "✅ Modules are up to date, skipping cleanup"
    exit 0
fi

echo "🧹 Cleaning up..."
# Only remove go.sum, keep the module cache unless explicitly requested
rm -f go.sum

# Clean only unused Docker build cache
docker builder prune -f --filter "until=24h"

echo "📦 Updating Go modules..."
# Tidy up go.mod and regenerate go.sum
go mod tidy

echo "🔄 Verifying modules..."
go mod verify

echo "🏗️ Building Docker image..."
# Build the image with build cache (remove --no-cache)
docker build -t noorbot .

echo "✨ Done!"
