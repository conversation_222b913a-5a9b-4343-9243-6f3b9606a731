package telegram

import (
	"context"
	"fmt"
	"testing"
	"time"

	"noorbotv2/internal/models"
)

// MockFileService implements the file service interface for testing
type MockFileService struct {
	documents map[int64][]*models.Document
}

func NewMockFileService() *MockFileService {
	return &MockFileService{
		documents: make(map[int64][]*models.Document),
	}
}

func (m *MockFileService) SaveFile(ctx context.Context, userID int64, docType models.DocumentType, fileData []byte, sanitizedFileName, originalFileName string, fileSize int64, mimeType string) (*models.Document, error) {
	doc := &models.Document{
		ID:        int64(len(m.documents[userID]) + 1),
		UserID:    userID,
		Name:      originalFileName,
		Type:      docType,
		Path:      "/tmp/" + sanitizedFileName,
		MIMEType:  mimeType,
		Size:      fileSize,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	m.documents[userID] = append(m.documents[userID], doc)
	return doc, nil
}

func (m *MockFileService) ListFilesByType(ctx context.Context, userID int64, docType models.DocumentType) ([]*models.Document, error) {
	var result []*models.Document
	for _, doc := range m.documents[userID] {
		if doc.Type == docType {
			result = append(result, doc)
		}
	}
	return result, nil
}

func (m *MockFileService) DeleteFile(ctx context.Context, userID int64, documentID int64) error {
	return nil
}

func (m *MockFileService) RenameFile(ctx context.Context, userID int64, documentID int64, newName string) error {
	return nil
}

func (m *MockFileService) MoveFile(ctx context.Context, userID int64, documentID int64, newType models.DocumentType, newFolder string) error {
	return nil
}

func (m *MockFileService) DownloadFile(ctx context.Context, userID int64, documentID int64) ([]byte, string, error) {
	return []byte("test content"), "test.txt", nil
}

func (m *MockFileService) ExtractTextFromFile(fileName string, mimeType string) (string, error) {
	return "extracted text", nil
}

// MockAIService implements the AI service interface for testing
type MockAIService struct{}

func (m *MockAIService) QueryDocuments(ctx context.Context, userID int64, query string) (string, error) {
	return "Mock AI response", nil
}

// MockCache implements a simple cache for testing
type MockCache struct {
	data map[string]interface{}
}

func NewMockCache() *MockCache {
	return &MockCache{
		data: make(map[string]interface{}),
	}
}

func (c *MockCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	c.data[key] = value
	return nil
}

func (c *MockCache) Get(ctx context.Context, key string, dest interface{}) error {
	return nil
}

func (c *MockCache) Incr(ctx context.Context, key string) (int64, error) {
	return 1, nil
}

func (c *MockCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return nil
}

func (c *MockCache) Health(ctx context.Context) error {
	return nil
}

// MockRateLimiter implements rate limiting for testing
type MockRateLimiter struct{}

func (r *MockRateLimiter) Allow(ctx context.Context, key string) (bool, error) {
	return true, nil
}

func TestFileListPagination(t *testing.T) {
	// Initialize test file service
	fileService := NewMockFileService()

	// Add test documents
	userID := int64(123)
	for i := 0; i < 15; i++ {
		_, err := fileService.SaveFile(
			context.Background(),
			userID,
			models.KnowledgeBase,
			[]byte("test content"),
			fmt.Sprintf("test%d.txt", i),
			fmt.Sprintf("test%d.txt", i),
			100,
			"text/plain",
		)
		if err != nil {
			t.Fatalf("Failed to save test file: %v", err)
		}
	}

	// Test file listing
	docs, err := fileService.ListFilesByType(context.Background(), userID, models.KnowledgeBase)
	if err != nil {
		t.Fatalf("Failed to list files: %v", err)
	}

	if len(docs) != 15 {
		t.Errorf("Expected 15 documents, got %d", len(docs))
	}

	// Test pagination logic
	const filesPerPage = 10
	totalPages := (len(docs) + filesPerPage - 1) / filesPerPage
	expectedPages := 2

	if totalPages != expectedPages {
		t.Errorf("Expected %d pages, got %d", expectedPages, totalPages)
	}

	// Test first page
	page := 0
	startIdx := page * filesPerPage
	endIdx := startIdx + filesPerPage
	if endIdx > len(docs) {
		endIdx = len(docs)
	}

	pageFiles := docs[startIdx:endIdx]
	if len(pageFiles) != 10 {
		t.Errorf("Expected 10 files on first page, got %d", len(pageFiles))
	}

	// Test second page
	page = 1
	startIdx = page * filesPerPage
	endIdx = startIdx + filesPerPage
	if endIdx > len(docs) {
		endIdx = len(docs)
	}

	pageFiles = docs[startIdx:endIdx]
	if len(pageFiles) != 5 {
		t.Errorf("Expected 5 files on second page, got %d", len(pageFiles))
	}
}

func TestFileActions(t *testing.T) {
	// Initialize test file service
	fileService := NewMockFileService()

	// Test file operations
	userID := int64(123)

	// Test save file
	doc, err := fileService.SaveFile(
		context.Background(),
		userID,
		models.Private,
		[]byte("test content"),
		"test.txt",
		"test.txt",
		100,
		"text/plain",
	)
	if err != nil {
		t.Fatalf("Failed to save file: %v", err)
	}

	// Test download file
	content, filename, err := fileService.DownloadFile(context.Background(), userID, doc.ID)
	if err != nil {
		t.Fatalf("Failed to download file: %v", err)
	}

	if string(content) != "test content" {
		t.Errorf("Expected 'test content', got '%s'", string(content))
	}

	if filename != "test.txt" {
		t.Errorf("Expected 'test.txt', got '%s'", filename)
	}

	// Test delete file
	err = fileService.DeleteFile(context.Background(), userID, doc.ID)
	if err != nil {
		t.Fatalf("Failed to delete file: %v", err)
	}
}
