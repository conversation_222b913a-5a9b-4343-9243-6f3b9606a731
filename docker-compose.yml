services:
  app:
    build: .
    container_name: noorbot-app
    env_file:
      - .env
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=noorbot
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=noorbot
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0
      - TELEGRAM_TOKEN=${TELEGRAM_TOKEN}
      - DEEPSEEK_TOKEN=${DEEPSEEK_TOKEN}
      - APP_PORT=8081
      - STORAGE_PATH=/app/storage
      - TEMP_PATH=/app/tmp
      - ENABLE_METRICS=true
      - METRICS_PORT=2112
      - ENV=production
      - DEBUG=false
    ports:
      - "8081:8081"
    volumes:
      - ./storage:/app/storage
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - bot_network
    healthcheck:
      test: ["<PERSON><PERSON>", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  postgres:
    image: pgvector/pgvector:pg16
    container_name: noorbot-postgres
    environment:
      POSTGRES_USER: noorbot
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: noorbot
    ports:
      - "5434:5432"  # Changed external port to 5434
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U noorbot"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - bot_network

  redis:
    image: redis:7-alpine
    container_name: noorbot-redis
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - bot_network

networks:
  bot_network:
    driver: bridge

volumes:
  postgres_data:
