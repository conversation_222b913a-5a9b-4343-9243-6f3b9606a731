package monitoring

import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promauto"
)

var (
    // Request metrics
    RequestDuration = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "request_duration_seconds",
            Help: "Time taken to process requests",
            Buckets: prometheus.DefBuckets,
        },
        []string{"handler", "status"},
    )

    // File metrics
    FileUploadTotal = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "file_uploads_total",
            Help: "Total number of file uploads",
        },
        []string{"type", "status"},
    )

    FileSize = promauto.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "file_size_bytes",
            Help: "Size of uploaded files in bytes",
            Buckets: []float64{1e3, 1e4, 1e5, 1e6, 1e7, 1e8},
        },
        []string{"type"},
    )

    // AI metrics
    AIQueryDuration = promauto.NewHistogram(
        prometheus.HistogramOpts{
            Name: "ai_query_duration_seconds",
            Help: "Time taken to process AI queries",
            Buckets: prometheus.DefBuckets,
        },
    )

    // Cache metrics
    CacheHits = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cache_hits_total",
            Help: "Total number of cache hits",
        },
        []string{"cache"},
    )

    CacheMisses = promauto.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cache_misses_total",
            Help: "Total number of cache misses",
        },
        []string{"cache"},
    )
)