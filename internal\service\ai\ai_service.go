package ai

import (
	"context"
	"fmt"
	"os"
	"strings"

	"noorbotv2/internal/config"
	"noorbotv2/internal/models"
	"noorbotv2/internal/repository"
	"noorbotv2/internal/service/cache"

	"go.uber.org/zap"
)

type Service struct {
	repo           repository.DocumentRepository
	cache          *cache.RedisCache
	logger         *zap.Logger
	deepSeekClient *DeepSeekClient
}

func NewAIService(
	repo repository.DocumentRepository,
	cache *cache.RedisCache,
	logger *zap.Logger,
	aiCfg config.AIConfig,
) (*Service, error) {
	// Initialize DeepSeek client
	deepSeekClient := NewDeepSeekClient(
		aiCfg.DeepSeekToken,
		aiCfg.DeepSeekModel,
		aiCfg.DeepSeekTemperature,
		aiCfg.DeepSeekMaxTokens,
	)

	return &Service{
		repo:           repo,
		cache:          cache,
		logger:         logger,
		deepSeekClient: deepSeekClient,
	}, nil
}

// QueryDocuments searches through documents using AI capabilities for a specific user
func (s *Service) QueryDocuments(ctx context.Context, userID int64, query string) (string, error) {
	// Log the query attempt
	s.logger.Info("Processing document query",
		zap.Int64("user_id", userID),
		zap.String("query", query))

	// Step 1: Generate embedding for the query
	queryEmbedding, err := s.generateQueryEmbedding(ctx, query)
	if err != nil {
		s.logger.Error("Failed to generate query embedding", zap.Error(err))
		return "I'm having trouble processing your query right now. Please try again later.", nil
	}

	// Step 2: Search for similar documents
	similarDocs, err := s.repo.SearchSimilarDocuments(ctx, userID, queryEmbedding, 5, nil)
	if err != nil {
		s.logger.Error("Failed to search similar documents", zap.Error(err))
		return "I couldn't search through your documents. Please try again later.", nil
	}

	if len(similarDocs) == 0 {
		return "I couldn't find any relevant documents to answer your question. Try uploading some documents first or rephrasing your query.", nil
	}

	// Step 3: Extract text content from relevant documents
	documentContext, err := s.buildDocumentContext(ctx, similarDocs)
	if err != nil {
		s.logger.Error("Failed to build document context", zap.Error(err))
		return "I found relevant documents but couldn't read their content. Please try again later.", nil
	}

	// Step 4: Generate AI response using document context
	aiResponse, err := s.generateContextualResponse(ctx, query, documentContext)
	if err != nil {
		s.logger.Error("Failed to generate AI response", zap.Error(err))
		return "I found relevant information but couldn't generate a response. Please try again later.", nil
	}

	s.logger.Info("Successfully processed document query",
		zap.Int64("user_id", userID),
		zap.Int("docs_found", len(similarDocs)))

	return aiResponse, nil
}

// generateQueryEmbedding creates an embedding for the user's query
func (s *Service) generateQueryEmbedding(ctx context.Context, query string) ([]float32, error) {
	// TODO: For now, return a mock embedding until we implement the actual embedding service
	// In a real implementation, this would use the SentenceTransformer or OpenAI embedding service
	s.logger.Warn("Using mock embedding - implement actual embedding generation")

	// Generate a mock embedding (1536 dimensions to match OpenAI text-embedding-3-small)
	mockEmbedding := make([]float32, 1536)
	for i := range mockEmbedding {
		mockEmbedding[i] = 0.1 // Simple mock value
	}

	return mockEmbedding, nil
}

// buildDocumentContext extracts and combines text content from relevant documents
func (s *Service) buildDocumentContext(ctx context.Context, docs []*models.Document) (string, error) {
	var contextParts []string

	for _, doc := range docs {
		// For now, we'll use the document name and basic info as context
		// TODO: Implement actual text extraction from files
		contextPart := fmt.Sprintf("Document: %s (Type: %s)", doc.Name, doc.Type)

		// Try to read some content if it's a text file
		if doc.MIMEType == "text/plain" {
			if content, err := os.ReadFile(doc.Path); err == nil {
				// Limit content to first 500 characters to avoid token limits
				contentStr := string(content)
				if len(contentStr) > 500 {
					contentStr = contentStr[:500] + "..."
				}
				contextPart += fmt.Sprintf("\nContent: %s", contentStr)
			}
		}

		contextParts = append(contextParts, contextPart)
	}

	return strings.Join(contextParts, "\n\n"), nil
}

// generateContextualResponse uses the DeepSeek API to generate a response based on the query and document context
func (s *Service) generateContextualResponse(ctx context.Context, query, documentContext string) (string, error) {
	// Create a prompt that includes the document context and user query
	prompt := fmt.Sprintf(`You are a helpful AI assistant that answers questions based on the provided documents.

Documents:
%s

User Question: %s

Please provide a helpful answer based on the information in the documents. If the documents don't contain enough information to answer the question, please say so clearly.`, documentContext, query)

	// Use the DeepSeek client to generate a response
	response, err := s.deepSeekClient.GenerateResponse(prompt)
	if err != nil {
		s.logger.Error("Failed to generate response from DeepSeek", zap.Error(err))
		return "", fmt.Errorf("failed to generate AI response: %w", err)
	}

	return response, nil
}
