package ai

import (
    "context"
    "go.uber.org/zap"
	"noorbotv2/internal/config" // Import config
    "noorbotv2/internal/repository"
    "noorbotv2/internal/service/cache"
)

type Service struct {
	repo           *repository.PostgresRepository
	cache          *cache.RedisCache
	logger         *zap.Logger
	deepSeekClient *DeepSeekClient // New field
	// embeddingService *EmbeddingService // Assuming embedding service might also be here
}

func NewAIService(
	repo *repository.PostgresRepository,
	cache *cache.RedisCache,
	logger *zap.Logger,
	aiCfg config.AIConfig, // Changed to use config.AIConfig
	// embeddingService *EmbeddingService, // If embedding service is separate
) (*Service, error) {
	// Initialize DeepSeekClient
	// Note: Error handling for NewDeepSeekClient if it could fail in the future
	// For now, NewDeepSeekClient doesn't return an error.
	// Also, ensure aiCfg.DeepSeekToken is available; otherwise, client might not be functional.
	// A check like `if aiCfg.DeepSeekToken == ""` might be needed.
	deepSeekClient := NewDeepSeekClient(
		aiCfg.DeepSeekToken,
		aiCfg.DeepSeekModel,
		aiCfg.DeepSeekTemperature,
		aiCfg.DeepSeekMaxTokens,
	)

    return &Service{
		repo:           repo,
		cache:          cache,
		logger:         logger,
		deepSeekClient: deepSeekClient,
		// embeddingService: embeddingService,
    }, nil
}

// QueryDocuments searches through documents using AI capabilities for a specific user
func (s *Service) QueryDocuments(ctx context.Context, userID int64, query string) (string, error) {
    // Log the query attempt
    s.logger.Info("Processing document query",
        zap.Int64("user_id", userID),
        zap.String("query", query))

    // TODO: Implement actual document querying logic
    // 1. Check user permissions
    // 2. Search through user's documents
    // 3. Generate AI response
    return "Query response placeholder", nil
}

