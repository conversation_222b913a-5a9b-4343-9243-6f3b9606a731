package health

import (
    "context"
    "sync"
    "time"
)

type HealthChecker struct {
    services map[string]HealthCheckable
    mu       sync.RWMutex
}

type HealthCheckable interface {
    Health() error
}

type Status struct {
    Status    string            `json:"status"`
    Services  map[string]string `json:"services"`
    Timestamp time.Time         `json:"timestamp"`
}

func NewHealthChecker() *HealthChecker {
    return &HealthChecker{
        services: make(map[string]HealthCheckable),
    }
}

func (h *HealthChecker) RegisterService(name string, service HealthCheckable) {
    h.mu.Lock()
    defer h.mu.Unlock()
    h.services[name] = service
}

func (h *HealthChecker) Check(ctx context.Context) Status {
    h.mu.RLock()
    defer h.mu.RUnlock()

    status := Status{
        Status:    "healthy",
        Services:  make(map[string]string),
        Timestamp: time.Now(),
    }

    for name, service := range h.services {
        if err := service.Health(); err != nil {
            status.Status = "unhealthy"
            status.Services[name] = err.Error()
        } else {
            status.Services[name] = "healthy"
        }
    }

    return status
}