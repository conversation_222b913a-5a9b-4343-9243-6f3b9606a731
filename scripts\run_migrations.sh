#!/bin/bash
set -e

echo "Running database migrations..."

# Wait for PostgreSQL to be ready
until PGPASSWORD=$DB_PASSWORD psql -h localhost -p 5434 -U noorbot -d noorbot -c '\q'; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

# Run migrations
for file in internal/repository/migrations/*.sql; do
    echo "Applying migration: $file"
    PGPASSWORD=$DB_PASSWORD psql -h localhost -p 5434 -U noorbot -d noorbot -f "$file"
done

echo "Migrations completed successfully!"