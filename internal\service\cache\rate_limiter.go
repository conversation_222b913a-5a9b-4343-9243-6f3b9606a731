package cache

import (
    "context"
    "fmt"
    "sync"
    "time"
)

type RateLimiter struct {
    cache     *RedisCache
    limit     int
    window    time.Duration
    mu        sync.Mutex
}

func NewRateLimiter(cache *RedisCache, limit int, window time.Duration) *RateLimiter {
    return &RateLimiter{
        cache:  cache,
        limit:  limit,
        window: window,
    }
}

func (rl *RateLimiter) Allow(ctx context.Context, key string) (bool, error) {
    rl.mu.Lock()
    defer rl.mu.Unlock()

    key = fmt.Sprintf("rate_limit:%s", key)
    
    count, err := rl.cache.Incr(ctx, key)
    if err != nil {
        return false, err
    }
    
    if count == 1 {
        if err := rl.cache.Expire(ctx, key, rl.window); err != nil {
            return false, err
        }
    }
    
    return count <= int64(rl.limit), nil
}

func (rl *RateLimiter) Reset(ctx context.Context, key string) error {
    rl.mu.Lock()
    defer rl.mu.Unlock()
    
    key = fmt.Sprintf("rate_limit:%s", key)
    return rl.cache.Set(ctx, key, 0, rl.window)
}
