package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time" // Already used by httpClient in NewSentenceTransformer

	"go.uber.org/zap" // Already used by logger field
	// "math" // No longer needed for mock normalization
	// "errors" // If you want to simulate errors
)

const openAIEmbeddingAPIURL = "https://api.openai.com/v1/embeddings"
const openAIEmbeddingModel = "text-embedding-3-small" // User-chosen model
const openAIEmbeddingDimensions = 1536                 // User-chosen dimension

// Request structure for OpenAI Embedding API
type OpenAIEmbeddingRequest struct {
	Input      string `json:"input"`
	Model      string `json:"model"`
	Dimensions int    `json:"dimensions,omitempty"` // Sent if value > 0
}

// Individual embedding data from OpenAI response
type OpenAIEmbeddingData struct {
	Object    string    `json:"object"`
	Embedding []float32 `json:"embedding"`
	Index     int       `json:"index"`
}

// Overall OpenAI Embedding API response structure
type OpenAIEmbeddingResponse struct {
	Object string                `json:"object"`
	Data   []OpenAIEmbeddingData `json:"data"`
	Model  string                `json:"model"`
	// Usage can be added here if needed
}

// Error details from OpenAI API
type OpenAIErrorDetail struct {
	Message string      `json:"message"`
	Type    string      `json:"type"`
	Param   interface{} `json:"param"` // Can be string or null
	Code    interface{} `json:"code"`  // Can be string or int
}

type OpenAIErrorResponse struct {
	Error OpenAIErrorDetail `json:"error"`
}


// SentenceTransformer will now act as a client for OpenAI's embedding API.
type SentenceTransformer struct {
	apiKey     string
	httpClient *http.Client
	logger     *zap.Logger
}

// NewSentenceTransformer initializes the client for OpenAI embeddings.
func NewSentenceTransformer(apiKey string, logger *zap.Logger) (*SentenceTransformer, error) {
	if apiKey == "" {
		return nil, fmt.Errorf("OpenAI API key is required for SentenceTransformer (OpenAI Embedding Client)")
	}
	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil for SentenceTransformer (OpenAI Embedding Client)")
	}
	return &SentenceTransformer{
		apiKey:     apiKey,
		httpClient: &http.Client{Timeout: 20 * time.Second}, // Sensible timeout
		logger:     logger,
	}, nil
}

func (st *SentenceTransformer) Encode(ctx context.Context, text string, normalize bool /* normalize is effectively ignored as OpenAI handles it */) ([]float32, error) {
    st.logger.Debug("Requesting OpenAI embedding", zap.String("text_preview", text[:min(50, len(text))])) // Assuming min helper exists

    requestPayload := OpenAIEmbeddingRequest{
        Input:      text,
        Model:      openAIEmbeddingModel,
    }
    // Only include dimensions if it's a positive value, as not all models might support it,
    // and text-embedding-3-small defaults to 1536 if not specified, but explicitly setting is good.
    if openAIEmbeddingDimensions > 0 {
        requestPayload.Dimensions = openAIEmbeddingDimensions
    }

    requestBodyBytes, err := json.Marshal(requestPayload)
    if err != nil {
        st.logger.Error("Failed to marshal OpenAI embedding request", zap.Error(err))
        return nil, fmt.Errorf("failed to create OpenAI embedding request body: %w", err)
    }

    req, err := http.NewRequestWithContext(ctx, "POST", openAIEmbeddingAPIURL, bytes.NewBuffer(requestBodyBytes))
    if err != nil {
        st.logger.Error("Failed to create new HTTP request for OpenAI embedding", zap.Error(err))
        return nil, fmt.Errorf("failed to create OpenAI embedding HTTP request: %w", err)
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer " + st.apiKey)

    resp, err := st.httpClient.Do(req)
    if err != nil {
        st.logger.Error("Failed to send HTTP request to OpenAI embedding API", zap.Error(err))
        return nil, fmt.Errorf("OpenAI embedding API request failed: %w", err)
    }
    defer resp.Body.Close()

    responseBodyBytes, err := io.ReadAll(resp.Body)
    if err != nil {
        st.logger.Error("Failed to read OpenAI embedding API response body", zap.Error(err))
        return nil, fmt.Errorf("failed to read OpenAI embedding API response: %w", err)
    }

    if resp.StatusCode != http.StatusOK {
        st.logger.Error("OpenAI embedding API returned non-OK status", 
            zap.Int("status_code", resp.StatusCode), 
            zap.String("response_body", string(responseBodyBytes)))
        var errResp OpenAIErrorResponse
        if json.Unmarshal(responseBodyBytes, &errResp) == nil && errResp.Error.Message != "" {
            return nil, fmt.Errorf("OpenAI API error: %s (Type: %s, Code: %v)", 
                errResp.Error.Message, errResp.Error.Type, errResp.Error.Code)
        }
        return nil, fmt.Errorf("OpenAI embedding API error: status %d, body: %s", resp.StatusCode, string(responseBodyBytes))
    }

    var apiResponse OpenAIEmbeddingResponse
    if err := json.Unmarshal(responseBodyBytes, &apiResponse); err != nil {
        st.logger.Error("Failed to unmarshal OpenAI embedding response", zap.Error(err), zap.String("response_body", string(responseBodyBytes)))
        return nil, fmt.Errorf("failed to parse OpenAI embedding response: %w", err)
    }

    if len(apiResponse.Data) == 0 || len(apiResponse.Data[0].Embedding) == 0 {
        st.logger.Warn("OpenAI embedding response contained no embedding data", zap.String("model_used", apiResponse.Model))
        return nil, fmt.Errorf("no embedding data received from OpenAI (model: %s)", apiResponse.Model)
    }
    
    st.logger.Info("Successfully received embedding from OpenAI", zap.String("model_used", apiResponse.Model))
    // The `openAIEmbeddingDimensions` constant should match the actual output dimension if specified,
    // or be verified against `len(apiResponse.Data[0].Embedding)`.
    if len(apiResponse.Data[0].Embedding) != openAIEmbeddingDimensions && openAIEmbeddingDimensions > 0 {
         st.logger.Warn("OpenAI embedding dimension mismatch", 
             zap.Int("requested", openAIEmbeddingDimensions),
             zap.Int("received", len(apiResponse.Data[0].Embedding)),
             zap.String("model", apiResponse.Model))
         // Return error or adapt, for now, return error if explicit dimension was requested and not met.
         return nil, fmt.Errorf("embedding dimension mismatch: requested %d, received %d from model %s", 
             openAIEmbeddingDimensions, len(apiResponse.Data[0].Embedding), apiResponse.Model)
    }

    return apiResponse.Data[0].Embedding, nil
}

// Helper for logging preview
func min(a, b int) int {
    if a < b {
        return a
    }
    return b
}
