package ai

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

const (
	deepSeekAPIURL = "https://api.deepseek.com/v1/chat/completions"
)

// DeepSeekClient handles interactions with the DeepSeek API.
type DeepSeekClient struct {
	apiKey      string
	modelName   string
	temperature float32
	maxTokens   int
	httpClient  *http.Client
}

// NewDeepSeekClient creates a new DeepSeek API client.
func NewDeepSeekClient(apiKey, modelName string, temperature float32, maxTokens int) *DeepSeekClient {
	return &DeepSeekClient{
		apiKey:      apiKey,
		modelName:   modelName,
		temperature: temperature,
		maxTokens:   maxTokens,
		httpClient: &http.Client{
			Timeout: 60 * time.Second, // Sensible default timeout
		},
	}
}

// Message represents a message in the chat completion request.
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// DeepSeekRequest represents the request payload for the DeepSeek API.
type DeepSeekRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Temperature float32   `json:"temperature"`
	MaxTokens   int       `json:"max_tokens"`
	// Other potential fields: top_p, frequency_penalty, presence_penalty
}

// DeepSeekResponseChoice represents a choice in the DeepSeek API response.
type DeepSeekResponseChoice struct {
	Message Message `json:"message"`
}

// DeepSeekResponse represents the response payload from the DeepSeek API.
type DeepSeekResponse struct {
	Choices []DeepSeekResponseChoice `json:"choices"`
	// Other fields like "created", "id", "model", "object", "usage" might exist
}

// GenerateResponse sends a prompt to the DeepSeek API and returns the model's response.
// This is a basic implementation for now.
func (c *DeepSeekClient) GenerateResponse(prompt string) (string, error) {
	requestPayload := DeepSeekRequest{
		Model: c.modelName, // Use field from struct
		Messages: []Message{
			{Role: "user", Content: prompt},
		},
		Temperature: c.temperature, // Use field from struct
		MaxTokens:   c.maxTokens,   // Use field from struct
	}

	payloadBytes, err := json.Marshal(requestPayload)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request payload: %w", err)
	}

	req, err := http.NewRequest("POST", deepSeekAPIURL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		return "", fmt.Errorf("failed to create HTTP request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.apiKey)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	var apiResponse DeepSeekResponse
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return "", fmt.Errorf("failed to decode API response: %w", err)
	}

	if len(apiResponse.Choices) > 0 && apiResponse.Choices[0].Message.Content != "" {
		return apiResponse.Choices[0].Message.Content, nil
	}

	return "", fmt.Errorf("no response text found or choices array is empty")
}
