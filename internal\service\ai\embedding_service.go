package ai

import (
    "noorbotv2/internal/service/cache"
)

type EmbeddingService struct {
    cache  *cache.RedisCache
    model  *OpenAIEmbeddingClient // Updated type
}

func NewEmbeddingService(cache *cache.RedisCache) (*EmbeddingService, error) {
    // Initialize your embedding model here
    model, err := NewOpenAIEmbeddingClient() // Updated constructor call
    if err != nil {
        // Propagate the error or handle it appropriately
        // For now, let's assume we return it.
        // In a real app, you might want to log this error as well.
        return nil, err
    }
    
    return &EmbeddingService{
        cache: cache,
        model: model,
    }, nil
}
