#!/bin/bash
set -euo pipefail

# Find the most recent backup directory
BACKUP_DIR=$(ls -td backup_* | head -n 1)

if [ -z "$BACKUP_DIR" ]; then
    echo "❌ No backup directory found"
    exit 1
fi

echo "📂 Using backup: $BACKUP_DIR"

# Create docs directory if it doesn't exist
mkdir -p docs

# Check for scope_full.md
if [ -f "$BACKUP_DIR/docs/scope_full.md" ]; then
    echo "✅ Found scope_full.md in backup"
    cp "$BACKUP_DIR/docs/scope_full.md" docs/scope_full.md
    echo "✨ Restored scope_full.md"
else
    echo "❌ scope_full.md not found in backup"
fi

# Check for AI_Protocol.md
if [ -f "$BACKUP_DIR/docs/AI_Protocol.md" ]; then
    echo "✅ Found AI_Protocol.md in backup"
    cp "$BACKUP_DIR/docs/AI_Protocol.md" docs/AI_Protocol.md
    echo "✨ Restored AI_Protocol.md"
else
    echo "❌ AI_Protocol.md not found in backup"
fi

# Copy migrations if they exist
if [ -d "backup_current/migrations" ]; then
    echo "✅ Found migrations in backup_current"
    mkdir -p internal/repository/migrations
    cp backup_current/migrations/* internal/repository/migrations/ 2>/dev/null || true
    echo "✨ Restored migrations"
fi

echo "✅ Backup verification complete!"
